<?php

require_once __ROOT__ . '/src/classes/source.php';

class Stakazo
{
    public string $id;
    public string $nombre;
    public Source $source;
    public float $costo;
    public float $cuota;
    public int $estado;
    public float $apuesta;
    public float $min_porc_profit;
    public float $min_porc_bank;
    public float $porc_bank;
    public float $bank;
    public string $bd_table           = 'bt_stakazos';
    public string $bd_alias           = 'btsta';
    public string $bd_id              = 'id';
    public string $bd_nombre          = 'nombre';
    public string $bd_id_source       = 'id_source';
    public string $bd_f_source_nombre = 'id_source';
    public string $bd_costo           = 'costo';
    public string $bd_cuota           = 'cuota';
    public string $bd_estado          = 'estado';

    function __construct()
    {
        $this->id              = '';
        $this->nombre          = '';
        $this->source          = new Source();
        $this->costo           = 0;
        $this->cuota           = 0;
        $this->estado          = 0;
        $this->apuesta         = 0;
        $this->min_porc_profit = 0;
        $this->min_porc_bank   = 0;
        $this->porc_bank       = 0;
        $this->bank            = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto                 = new self;
            $objeto->id             = desordena($resultado[$cq->bd_id]);
            $objeto->nombre         = $resultado[$cq->bd_nombre];
            $objeto->source->id     = desordena($resultado[$cq->bd_id_source]);
            $objeto->source->nombre = (isset($resultado["$cq->bd_f_source_nombre"])) ? $resultado["$cq->bd_f_source_nombre"] : "";
            $objeto->costo          = $resultado[$cq->bd_costo];
            $objeto->cuota          = $resultado[$cq->bd_cuota];
            $objeto->estado         = $resultado[$cq->bd_estado];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($paramref, $conexion): self
    {
        try {
            $id     = (isset($paramref["id"])) ? $paramref["id"] : "";
            $nombre = (isset($paramref["nombre"])) ? $paramref["nombre"] : "";

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id > 0 ";

            if(!empty($id)){
                $query .= "AND $cqa.$cq->bd_id = :$cq->bd_id ";
            }
            if(!empty($nombre)){
                $query .= "AND $cqa.$cq->bd_nombre = :$cq->bd_nombre ";
            }

            $statement = $conexion->prepare($query);

            if(!empty($id)){
                $statement->bindValue(":$cq->bd_id", ordena($id));
            }
            if(!empty($nombre)){
                $statement->bindValue(":$cq->bd_nombre", $nombre);
            }
            
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list($paramref, $conexion): array
    {
        try {
            $cq  = new self;
            $cqa = $cq->bd_alias;
            $cq_so = new Source();
            $cq_so_a = $cq_so->bd_alias;

            $query = "SELECT ";
            $query .= "   $cqa.* ";
            $query .= "  ,$cq_so_a.$cq_so->bd_nombre $cq->bd_f_source_nombre ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "INNER JOIN $cq_so->bd_table $cq_so_a ";
            $query .= "  ON ($cq_so_a.$cq_so->bd_id = $cqa.$cq->bd_id_source) ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = 1 ";

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function agregar(PDO $conexion): void
    {
        try {
            $this->validate_data($conexion);			

            $cq = new self;

            $query  = "INSERT INTO $cq->bd_table (";
            $query .= "   $cq->bd_nombre ";
            $query .= "  ,$cq->bd_id_source ";
            $query .= "  ,$cq->bd_costo ";
            $query .= "  ,$cq->bd_cuota ";
            $query .= ") VALUES (";
            $query .= "   :$cq->bd_nombre ";
            $query .= "  ,:$cq->bd_id_source ";
            $query .= "  ,:$cq->bd_costo ";
            $query .= "  ,:$cq->bd_cuota ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->bindValue(":$cq->bd_id_source", ordena($this->source->id));
            $statement->bindValue(":$cq->bd_costo", $this->costo);
            $statement->bindValue(":$cq->bd_cuota", $this->cuota);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modificar(PDO $conexion): void
    {
        try {
            $this->validate_data($conexion);			

            $cq = new self;

            $query  = " UPDATE $cq->bd_table SET ";
            $query .= "   $cq->bd_nombre = :$cq->bd_nombre ";
            $query .= "  ,$cq->bd_id_source = :$cq->bd_id_source ";
            $query .= "  ,$cq->bd_costo = :$cq->bd_costo ";
            $query .= "  ,$cq->bd_cuota = :$cq->bd_cuota ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->bindValue(":$cq->bd_id_source", ordena($this->source->id));
            $statement->bindValue(":$cq->bd_costo", $this->costo);
            $statement->bindValue(":$cq->bd_cuota", $this->cuota);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function calcular(): void
    {
        try {
            $this->calcular_apuesta();
            $this->calcular_porc_bank();
    
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function calcular_apuesta(): void
    {
        try {
            $this->apuesta = ($this->costo / ($this->cuota - (1 + ($this->min_porc_profit / 100))));
    
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function calcular_porc_bank(): void
    {
        try {
            $this->porc_bank = round(($this->apuesta * 100) / $this->bank, 2);
    
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data($conexion): void
    {
        try {
            validar_textovacio($this->nombre, 'Debe especificar el nombre.');
            validar_textovacio($this->source->nombre, 'Debe especificar el source.');

            $this->nombre     = mb_strtoupper($this->nombre);     
            $this->source->id = Source::get(array('nombre' => $this->source->nombre), $conexion)->id;

            if(empty($this->source->id)){
                throw new Exception("No se encontro ese source en el sistema");
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>