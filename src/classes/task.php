<?php

class Task {
	public $id_task;
	public $id_project;
	public $name;
	public $note;
	public $done;
	public $done_date;
	public $work;
	public $module;
	public $urgent;
	public $state;

	public static function get_list($id_project,$isdone,$conexion) {
		try {
			$query = "SELECT * 
					  FROM tasks 
					  WHERE id_project = :id_project AND state = 1 AND done = :isdone ";

			if($isdone == 1){
				$query .= " ORDER BY done_date DESC ";
				$query .= " LIMIT 10 ";
			} else{
				$query .= " ORDER BY module, name ";
			}
			$statement = $conexion->prepare($query);
			$statement->execute(array(
				':id_project' => $id_project,
				':isdone' => $isdone,
			));
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$tasks = array();
	
				foreach ($resultados as $resultado) {
					$task = new Task;
					$task->id_task = $resultado['id_task'];
					$task->id_project = $resultado['id_project'];
					$task->name = html_entity_decode($resultado['name']);
					$task->note = html_entity_decode($resultado['note']);
					$task->done = $resultado['done'];
					$task->work = $resultado['work'];
					$task->urgent = $resultado['urgent'];
					$task->module = $resultado['module'];					
					
					array_push($tasks, $task);
				}
	
				return $tasks;
			}
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	function add($conexion) {
		try {
			$statement = $conexion->prepare(
				'INSERT INTO tasks (id_project, name, note, module)
				 VALUES (:id_project, :name, :note, :module)'
			);
			$statement->execute(array(
				':id_project' => $this->id_project,
				':name' => $this->name,
				':note' => $this->note,
				':module' => $this->module,				
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	public static function done($id_task,$conexion) {
		try {
			$done_date = create_datetime();

			$statement = $conexion->prepare(
				'UPDATE tasks 
				 SET done = 1, done_date = :done_date, work = 0
				 WHERE id_task = :id_task'
			);
			$statement->execute(array(
				':id_task' => $id_task,
				':done_date' => $done_date,
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	public static function work($id_task,$conexion) {
		try {
			$statement = $conexion->prepare(
				'UPDATE tasks 
				 SET work = 1 
				 WHERE id_task = :id_task'
			);
			$statement->execute(array(
				':id_task' => $id_task,
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	public static function delete($id_task,$conexion) {
		try {
			$statement = $conexion->prepare(
				'UPDATE tasks 
				 SET state = 0 
				 WHERE id_task = :id_task'
			);
			$statement->execute(array(
				':id_task' => $id_task,
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	public static function disregard($id_task,$conexion) {
		try {
			$statement = $conexion->prepare(
				'UPDATE tasks 
				 SET done = 0, work = 0, urgent = 0 
				 WHERE id_task = :id_task'
			);
			$statement->execute(array(
				':id_task' => $id_task,
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	public static function urgent($id_task,$conexion) {
		try {
			$statement = $conexion->prepare(
				'UPDATE tasks 
				 SET urgent = 1 
				 WHERE id_task = :id_task'
			);
			$statement->execute(array(
				':id_task' => $id_task,
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}

	function modify($conexion) {
		try {
			$statement = $conexion->prepare(
				'UPDATE tasks 
				 SET name = :name, module = :module, note = :note 
				 WHERE id_task = :id_task'
			);
			$statement->execute(array(
				':id_task' => $this->id_task,
				':name' => $this->name,
				':module' => $this->module,
				':note' => $this->note,
			));
		} catch (PDOException $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>