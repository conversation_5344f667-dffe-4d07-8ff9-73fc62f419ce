<?php

class Objective
{
    public $id_objective;
    public $module;
    public $description;
    public $note;
    public $solicitante;
    public $proyecto;
    public $priority;
    public $done;
    public $donefch;
    public $times;
    public $lasthit;
    public $pinned;
    public $state;
    const PRIORIDAD_HIGH = 1;
    const PRIORIDAD_MEDIUM = 2;
    const PRIORIDAD_LOW = 3;
    const PRIORIDAD_NONE = 4;
    const TIPO_SOLICITANTE = 1;
    const TIPO_PROYECTO = 2;

    public static function construct($resultado): Objective
    {
        $objective = new Objective;
        $objective->id_objective = desordena($resultado['id_objective']);
        $objective->module = $resultado['module'];
        $objective->description = $resultado['description'];
        $objective->note = $resultado['note'];
        $objective->solicitante = $resultado['solicitante'];
        $objective->proyecto = $resultado['proyecto'];
        $objective->priority = $resultado['priority'];
        $objective->done = $resultado['done'];
        $objective->donefch = $resultado['donefch'];
        $objective->times = $resultado['times'];
        $objective->lasthit = $resultado['lasthit'];
        $objective->pinned = $resultado['pinned'];
        $objective->state = $resultado['state'];

        return $objective;
    }

    public static function construct_bosshp($resultado): array
    {
        $objective = array();
        $objective['id_objective'] = desordena($resultado['id_objective']);
        $objective['boss'] = $resultado['boss'];
        $objective['hp'] = $resultado['hp'];
        $objective['donehp'] = $resultado['donehp'];
        $objective['hpleft'] = Objective::calc_bosshpleft($objective['hp'],$objective['donehp']);
        $objective['lasthithp'] = $resultado['lasthithp'];
        $objective['allpend'] = $resultado['allpend'];
        $objective['allpinned'] = $resultado['allpinned'];
        $objective['allpriohigh'] = $resultado['allpriohigh'];
        $objective['allpriomed'] = $resultado['allpriomed'];
        $objective['allpriolow'] = $resultado['allpriolow'];
        $objective['allprionone'] = $resultado['allprionone'];

        $objective['porcbosshp'] = round(($objective['hpleft'] * 100) / $objective['hp'],2);
        $objective['porclasthithp'] = round(($objective['lasthithp'] * 100) / $objective['hp'],2);
        $objective['bgcolor'] = 'bg-success';

        if($objective['porcbosshp'] < 45 && $objective['porcbosshp'] >= 25){
            $objective['bgcolor'] = 'bg-warning';

        } elseif($objective['porcbosshp'] < 25){
            $objective['bgcolor'] = 'bg-danger';
        }

        return $objective;
    }

    // usado en:
    public static function get($id_objective, $conexion): Objective
    {
        try {
            $query = 'SELECT ';
            $query .= '  o.* ';
            $query .= 'FROM objectives o ';
            $query .= 'WHERE ';
            $query .= '  id_objective = :id_objective ';

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_objective', ordena($id_objective));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new Objective;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    // usado en:
    public static function get_list($pinned,$tipo,$boss,$priority,$conexion)
    {
        try {
            $query  = 'SELECT';
            $query .= '  o.*';
            $query .= 'FROM objectives o ';
            $query .= 'WHERE ';
            $query .= '  o.state = 1 ';
            $query .= '  AND o.done = 0 ';

            if(!empty($priority)){
                $query .= '  AND o.priority = :priority ';
            }

            $query .= '  AND o.pinned = :pinned ';

            switch ($tipo) {
                case self::TIPO_SOLICITANTE:
                    $query .= 'AND o.solicitante = :solicitante ';

                    break;
                case self::TIPO_PROYECTO:
                    $query .= 'AND o.proyecto = :proyecto ';

                    break;
            }

            $query .= 'ORDER BY ';
            $query .= '  o.module ';

            $statement = $conexion->prepare($query);

            switch ($tipo) {
                case self::TIPO_SOLICITANTE:
                    $statement->bindValue(':solicitante', $boss);

                    break;
                case self::TIPO_PROYECTO:
                    $statement->bindValue(':proyecto', $boss);

                    break;
            }

            if(!empty($priority)){
                $statement->bindValue(':priority', $priority);
            }

            $statement->bindValue(':pinned', $pinned);

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $objectives = array();

                foreach ($resultados as $resultado) {
                    $objective = self::construct($resultado);

                    array_push($objectives, $objective);
                }

                return $objectives;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public static function get_listboss($conexion)
    {
        try {
            $proyectos = self::get_listgroupbyproyecto('',$conexion);
            $solicitantes = self::get_listgroupbysolicitante('',$conexion);
            $bosses = array();

            foreach ($proyectos as $proyecto) {
                if($proyecto['hpleft'] > 0){
                    $bosses[] = $proyecto;
                }
            }

            foreach ($solicitantes as $solicitante) {
                if($solicitante['hpleft'] > 0){
                    $bosses[] = $solicitante;
                }
            }

            usort($bosses, function ($a, $b) {
                return $a['boss'] <=> $b['boss'];
            });

            return $bosses;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public static function get_listgroupbysolicitante($solicitante,$conexion): array
    {
        try {
            $query  = 'SELECT ';
            $query .= '  o.id_objective ';
            $query .= '  ,o.solicitante boss ';
            $query .= '  ,SUM(o.times * 100) hp ';
            $query .= '  ,IFNULL(done.hp, 0) donehp ';
            $query .= '  ,IFNULL(lasthit.hp, 0) lasthithp ';
            $query .= '  ,IFNULL(allpend.nregs, 0) allpend ';
            $query .= '  ,IFNULL(allpinned.nregs, 0) allpinned ';
            $query .= '  ,IFNULL(allpriohigh.nregs, 0) allpriohigh ';
            $query .= '  ,IFNULL(allpriomed.nregs, 0) allpriomed ';
            $query .= '  ,IFNULL(allpriolow.nregs, 0) allpriolow ';
            $query .= '  ,IFNULL(allprionone.nregs, 0) allprionone ';
            $query .= 'FROM ';
            $query .= '  objectives o ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.solicitante ';
            $query .= '    ,SUM(o.times * 100) hp ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 1 ';
            $query .= '  GROUP BY ';
            $query .= '    o.solicitante ';
            $query .= ') AS done ON (done.solicitante = o.solicitante) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.solicitante ';
            $query .= '    ,SUM(o.times * 100) hp ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.lasthit = 1 ';
            $query .= '  GROUP BY ';
            $query .= '    o.solicitante ';
            $query .= ') AS lasthit ON (lasthit.solicitante = o.solicitante) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.solicitante ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '  GROUP BY ';
            $query .= '    o.solicitante ';
            $query .= ') AS allpend ON (allpend.solicitante = o.solicitante) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.solicitante ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.pinned = 1 ';
            $query .= '  GROUP BY ';
            $query .= '    o.solicitante ';
            $query .= ') AS allpinned ON (allpinned.solicitante = o.solicitante) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.solicitante ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.priority = :priorityallpriohigh ';
            $query .= '  GROUP BY ';
            $query .= '    o.solicitante ';
            $query .= ') AS allpriohigh ON (allpriohigh.solicitante = o.solicitante) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.solicitante ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.priority = :priorityallpriomed ';
            $query .= '  GROUP BY ';
            $query .= '    o.solicitante ';
            $query .= ') AS allpriomed ON (allpriomed.solicitante = o.solicitante) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.solicitante ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.priority = :priorityallpriolow ';
            $query .= '  GROUP BY ';
            $query .= '    o.solicitante ';
            $query .= ') AS allpriolow ON (allpriolow.solicitante = o.solicitante) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.solicitante ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.priority = :priorityallprionone ';
            $query .= '  GROUP BY ';
            $query .= '    o.solicitante ';
            $query .= ') AS allprionone ON (allprionone.solicitante = o.solicitante) ';
            $query .= 'WHERE ';
            $query .= '  o.state = 1 ';

            if(!empty($solicitante)){
                $query .= 'AND o.solicitante = :solicitante ';
            }

            $query .= 'GROUP BY ';
            $query .= '  o.solicitante ';
            $statement = $conexion->prepare($query);

            if(!empty($solicitante)){
                $statement->bindValue(':solicitante', $solicitante);
            }

            $statement->bindValue(':priorityallpriohigh', self::PRIORIDAD_HIGH);
            $statement->bindValue(':priorityallpriomed', self::PRIORIDAD_MEDIUM);
            $statement->bindValue(':priorityallpriolow', self::PRIORIDAD_LOW);
            $statement->bindValue(':priorityallprionone', self::PRIORIDAD_NONE);

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $objectives = array();

                foreach ($resultados as $resultado) {
                    $objective = self::construct_bosshp($resultado);
                    $objective['tipo'] = self::TIPO_SOLICITANTE;

                    $objectives[] = $objective;
                }

                return $objectives;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public static function get_listgroupbyproyecto($proyecto,$conexion): array
    {
        try {
            $query  = 'SELECT ';
            $query .= '  o.id_objective ';
            $query .= '  ,o.proyecto boss ';
            $query .= '  ,SUM(o.times * 100) hp ';
            $query .= '  ,IFNULL(done.hp, 0) donehp ';
            $query .= '  ,IFNULL(lasthit.hp, 0) lasthithp ';
            $query .= '  ,IFNULL(allpend.nregs, 0) allpend ';
            $query .= '  ,IFNULL(allpinned.nregs, 0) allpinned ';
            $query .= '  ,IFNULL(allpriohigh.nregs, 0) allpriohigh ';
            $query .= '  ,IFNULL(allpriomed.nregs, 0) allpriomed ';
            $query .= '  ,IFNULL(allpriolow.nregs, 0) allpriolow ';
            $query .= '  ,IFNULL(allprionone.nregs, 0) allprionone ';
            $query .= 'FROM ';
            $query .= '  objectives o ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.proyecto ';
            $query .= '    ,SUM(o.times * 100) hp ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 1 ';
            $query .= '  GROUP BY ';
            $query .= '    o.proyecto ';
            $query .= ') AS done ON (done.proyecto = o.proyecto) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.proyecto ';
            $query .= '    ,SUM(o.times * 100) hp ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.lasthit = 1 ';
            $query .= '  GROUP BY ';
            $query .= '    o.proyecto ';
            $query .= ') AS lasthit ON (lasthit.proyecto = o.proyecto) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.proyecto ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '  GROUP BY ';
            $query .= '    o.proyecto ';
            $query .= ') AS allpend ON (allpend.proyecto = o.proyecto) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.proyecto ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.pinned = 1 ';
            $query .= '  GROUP BY ';
            $query .= '    o.proyecto ';
            $query .= ') AS allpinned ON (allpinned.proyecto = o.proyecto) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.proyecto ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.priority = :priorityallpriohigh ';
            $query .= '  GROUP BY ';
            $query .= '    o.proyecto ';
            $query .= ') AS allpriohigh ON (allpriohigh.proyecto = o.proyecto) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.proyecto ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.priority = :priorityallpriomed ';
            $query .= '  GROUP BY ';
            $query .= '    o.proyecto ';
            $query .= ') AS allpriomed ON (allpriomed.proyecto = o.proyecto) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.proyecto ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.priority = :priorityallpriolow ';
            $query .= '  GROUP BY ';
            $query .= '    o.proyecto ';
            $query .= ') AS allpriolow ON (allpriolow.proyecto = o.proyecto) ';
            $query .= 'LEFT JOIN( ';
            $query .= '  SELECT ';
            $query .= '    o.proyecto ';
            $query .= '    ,COUNT(o.id_objective) nregs ';
            $query .= '  FROM ';
            $query .= '    objectives o ';
            $query .= '  WHERE ';
            $query .= '    o.state = 1  ';
            $query .= '    AND o.done = 0 ';
            $query .= '    AND o.priority = :priorityallprionone ';
            $query .= '  GROUP BY ';
            $query .= '    o.proyecto ';
            $query .= ') AS allprionone ON (allprionone.proyecto = o.proyecto) ';
            $query .= 'WHERE ';
            $query .= '  o.state = 1 ';

            if(!empty($proyecto)){
                $query .= 'AND o.proyecto = :proyecto ';
            }

            $query .= 'GROUP BY ';
            $query .= '  o.proyecto ';
            $statement = $conexion->prepare($query);

            if(!empty($proyecto)){
                $statement->bindValue(':proyecto', $proyecto);
            }

            $statement->bindValue(':priorityallpriohigh', self::PRIORIDAD_HIGH);
            $statement->bindValue(':priorityallpriomed', self::PRIORIDAD_MEDIUM);
            $statement->bindValue(':priorityallpriolow', self::PRIORIDAD_LOW);
            $statement->bindValue(':priorityallprionone', self::PRIORIDAD_NONE);

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $objectives = array();

                foreach ($resultados as $resultado) {
                    $objective = self::construct_bosshp($resultado);
                    $objective['tipo'] = self::TIPO_PROYECTO;

                    $objectives[] = $objective;
                }

                return $objectives;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    // usado en:
    public static function get_listsolicitantes($conexion)
    {
        try {
            $query  = 'SELECT ';
            $query .= '  o.solicitante ';
            $query .= 'FROM objectives o ';
            $query .= 'WHERE ';
            $query .= '  o.state = 1 ';
            $query .= 'GROUP BY ';
            $query .= '  o.solicitante ';
            $query .= 'ORDER BY ';
            $query .= '  o.solicitante ';

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $solicitantes = array();

                foreach ($resultados as $resultado) {
                    $solicitante = array();
                    $solicitante['solicitante'] = $resultado['solicitante'];

                    $solicitantes[] = $solicitante;
                }

                return $solicitantes;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    // usado en:
    public static function get_listproyectos($conexion)
    {
        try {
            $query  = 'SELECT ';
            $query .= '  o.proyecto ';
            $query .= 'FROM objectives o ';
            $query .= 'WHERE ';
            $query .= '  o.state = 1 ';
            $query .= 'GROUP BY ';
            $query .= '  o.proyecto ';
            $query .= 'ORDER BY ';
            $query .= '  o.proyecto ';

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $proyectos = array();

                foreach ($resultados as $resultado) {
                    $proyecto = array();
                    $proyecto['proyecto'] = $resultado['proyecto'];

                    $proyectos[] = $proyecto;
                }

                return $proyectos;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion)
    {
        try {
            // BEGIN validations
            validar_textovacio($this->module, 'Specify module');
            validar_textovacio($this->description, 'Specify description');
            validar_textovacio($this->priority, 'Specify priority');
            validar_textovacio($this->solicitante, 'Specify solicitante');
            validar_textovacio($this->proyecto, 'Specify proyecto');
            // END validations

            $query = 'INSERT INTO objectives (';
            $query .= '  module ';
            $query .= '  ,description ';
            $query .= '  ,note ';
            $query .= '  ,solicitante ';
            $query .= '  ,proyecto ';
            $query .= '  ,priority ';
            $query .= '  ,times ';
            $query .= ') VALUES (';
            $query .= '  :module ';
            $query .= '  ,:description ';
            $query .= '  ,:note ';
            $query .= '  ,:solicitante ';
            $query .= '  ,:proyecto ';
            $query .= '  ,:priority ';
            $query .= '  ,:times ';
            $query .= ') ';

            $statement = $conexion->prepare($query);
            $statement->bindValue(':module', trim($this->module));
            $statement->bindValue(':description', trim($this->description));
            $statement->bindValue(':note', trim($this->note));
            $statement->bindValue(':solicitante', trim($this->solicitante));
            $statement->bindValue(':proyecto', trim($this->proyecto));
            $statement->bindValue(':priority', $this->priority);
            $statement->bindValue(':times', $this->times);
            $statement->execute();

            $this->id_objective = desordena($conexion->lastInsertId());

            if($this->pinned == 1){
                $this->modify_pinned($conexion);
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    function modify($conexion)
    {
        try {
            // BEGIN validations
            validar_textovacio($this->module, 'Specify module');
            validar_textovacio($this->description, 'Specify description');
            validar_textovacio($this->priority, 'Specify priority');
            validar_textovacio($this->solicitante, 'Specify solicitante');
            validar_textovacio($this->proyecto, 'Specify proyecto');
            // END validations

            $query = 'UPDATE objectives SET';
            $query .= '  module = :module ';
            $query .= '  ,description = :description ';
            $query .= '  ,note = :note ';
            $query .= '  ,solicitante = :solicitante ';
            $query .= '  ,proyecto = :proyecto ';
            $query .= '  ,priority = :priority ';
            $query .= '  ,times = :times ';
            $query .= 'WHERE ';
            $query .= '  id_objective = :id_objective ';

            $statement = $conexion->prepare($query);
            $statement->bindValue(':module', trim($this->module));
            $statement->bindValue(':description', trim($this->description));
            $statement->bindValue(':note', trim($this->note));
            $statement->bindValue(':solicitante', trim($this->solicitante));
            $statement->bindValue(':proyecto', trim($this->proyecto));
            $statement->bindValue(':priority', $this->priority);
            $statement->bindValue(':times', $this->times);
            $statement->bindValue(':id_objective', ordena($this->id_objective));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    function modify_done($conexion)
    {
        try {
            $this->modify_lasthiteraseall($conexion);

            $fchdone = create_date();

            $query = 'UPDATE objectives SET ';
            $query .= '  done = 1 ';
            $query .= '  ,lasthit = 1 ';
            $query .= '  ,donefch = :donefch ';
            $query .= 'WHERE ';
            $query .= '  id_objective = :id_objective ';

            $statement = $conexion->prepare($query);
            $statement->bindValue(':donefch', $fchdone);
            $statement->bindValue(':id_objective', ordena($this->id_objective));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    function modify_lasthiteraseall($conexion)
    {
        try {
            $query = 'UPDATE objectives SET ';
            $query .= '  lasthit = 0 ';
            $query .= 'WHERE ';
            $query .= '  id_objective > 0 ';

            $statement = $conexion->prepare($query);
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    function modify_pinned($conexion)
    {
        try {
            $this->modify_pinnederaseall($conexion);

            $query = 'UPDATE objectives SET ';
            $query .= '  pinned = 1 ';
            $query .= 'WHERE ';
            $query .= '  id_objective = :id_objective ';

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_objective', ordena($this->id_objective));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    function modify_pinnederaseall($conexion)
    {
        try {
            $query = 'UPDATE objectives SET ';
            $query .= '  pinned = 0 ';
            $query .= 'WHERE ';
            $query .= '  id_objective > 0 ';

            $statement = $conexion->prepare($query);
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    function modify_unpinned($conexion)
    {
        try {
            $query = 'UPDATE objectives SET ';
            $query .= '  pinned = 0 ';
            $query .= 'WHERE ';
            $query .= '  id_objective = :id_objective ';

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_objective', ordena($this->id_objective));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    function delete($conexion)
    {
        try {
            $query = 'UPDATE objectives SET ';
            $query .= '  state = 0 ';
            $query .= 'WHERE ';
            $query .= '  id_objective = :id_objective ';

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_objective', ordena($this->id_objective));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public static function calc_bosshpleft($totalhp, $donehp){
        return $totalhp - $donehp;
    }
}

?>