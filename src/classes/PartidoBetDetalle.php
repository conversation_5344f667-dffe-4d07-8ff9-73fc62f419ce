<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents a partido bet detail record.
 * Assumes global functions ordena(), desordena(), validar_textovacio(), format_numberclean() are available.
 */
class PartidoBetDetalle
{
    // --- Attributes ---
    private ?string $id = null; // Stores the 'desordenado' ID
    private ?string $id_partido_bet = null;
    private ?string $id_apuesta_tipo = null;
    private ?float $cuota = null;
    private ?string $id_partido = null;

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id = null;
        $this->id_partido_bet = null;
        $this->id_apuesta_tipo = null;
        $this->cuota = null;
        $this->id_partido = null;
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of PartidoBetDetalle.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            
            // The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                    error_log("desordena() returned empty/null for DB ID: " . $dbId);
                    throw new Exception("Error processing PartidoBetDetalle ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }

            // Handle id_partido_bet
            $dbIdPartidoBet = $data['id_partido_bet'] ?? null;
            if ($dbIdPartidoBet !== null) {
                $desordenadoIdPartidoBet = desordena((string)$dbIdPartidoBet);
                $objeto->id_partido_bet = $desordenadoIdPartidoBet;
            } else {
                $objeto->id_partido_bet = null;
            }

            // Handle id_apuesta_tipo
            $dbIdApuestaTipo = $data['id_apuesta_tipo'] ?? null;
            if ($dbIdApuestaTipo !== null) {
                $desordenadoIdApuestaTipo = desordena((string)$dbIdApuestaTipo);
                $objeto->id_apuesta_tipo = $desordenadoIdApuestaTipo;
            } else {
                $objeto->id_apuesta_tipo = null;
            }

            // Handle id_partido
            $dbIdPartido = $data['id_partido'] ?? null;
            if ($dbIdPartido !== null) {
                $desordenadoIdPartido = desordena((string)$dbIdPartido);
                $objeto->id_partido = $desordenadoIdPartido;
            } else {
                $objeto->id_partido = null;
            }

            $objeto->cuota = isset($data['cuota']) ? (float)$data['cuota'] : null;

            return $objeto;

        } catch (Exception $e) {
            throw new Exception("Failed to construct PartidoBetDetalle from data: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a PartidoBetDetalle object from the database by its 'desordenado' string ID.
     *
     * @param string $id The 'desordenado' string ID of the PartidoBetDetalle to retrieve.
     * @param PDO $conexion The database connection object.
     * @return self|null A PartidoBetDetalle object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM partidos_bets_detalles
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            if ($resultado === false) {
                return null; // No record found
            }

            return self::construct($resultado);

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving PartidoBetDetalle: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all PartidoBetDetalle objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of PartidoBetDetalle objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM partidos_bets_detalles
            ORDER BY id DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $partidoBetDetalles = [];
            foreach ($resultados as $resultado) {
                $partidoBetDetalles[] = self::construct($resultado);
            }

            return $partidoBetDetalles;

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving PartidoBetDetalle list: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of PartidoBetDetalle objects by partido bet ID.
     *
     * @param string $idPartidoBet The 'desordenado' string ID of the partido bet.
     * @param PDO $conexion The database connection object.
     * @return array An array of PartidoBetDetalle objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getByPartidoBet(string $idPartidoBet, PDO $conexion): array
    {
        if (empty(trim($idPartidoBet))) {
            throw new InvalidArgumentException("Invalid partido bet ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID for the query
            $idPartidoBetOrdenado = ordena($idPartidoBet);
            if ($idPartidoBetOrdenado === false || $idPartidoBetOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided partido bet ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM partidos_bets_detalles
            WHERE id_partido_bet = :id_partido_bet
            ORDER BY id DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_partido_bet', $idPartidoBetOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $partidoBetDetalles = [];
            foreach ($resultados as $resultado) {
                $partidoBetDetalles[] = self::construct($resultado);
            }

            return $partidoBetDetalles;

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving PartidoBetDetalle list by partido bet: " . $e->getMessage());
        }
    }

    /**
     * Saves (inserts or updates) the current PartidoBetDetalle instance to the database.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("PartidoBetDetalle::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("PartidoBetDetalle::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            if ($isInsertOperation) {
                return $this->_insert($conexion);
            } else {
                return $this->_update($conexion);
            }
        } catch (PDOException $e) {
            error_log("PartidoBetDetalle::guardar() - PDO Error: " . $e->getMessage());
            throw new Exception("Database error during save operation: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current PartidoBetDetalle instance into the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        $query = <<<SQL
        INSERT INTO partidos_bets_detalles (
            id_partido_bet,
            id_apuesta_tipo,
            cuota,
            id_partido
        ) VALUES (
            :id_partido_bet,
            :id_apuesta_tipo,
            :cuota,
            :id_partido
        )
        SQL;

        $statement = $conexion->prepare($query);

        // Convert all foreign key IDs to ordenado for database storage
        $idPartidoBetOrdenado = null;
        if ($this->getIdPartidoBet() !== null) {
            $idPartidoBetOrdenado = ordena($this->getIdPartidoBet());
            if ($idPartidoBetOrdenado === false || $idPartidoBetOrdenado <= 0) {
                throw new Exception("Failed to process the partido bet ID for insert: " . $this->getIdPartidoBet());
            }
        }

        $idApuestaTipoOrdenado = null;
        if ($this->getIdApuestaTipo() !== null) {
            $idApuestaTipoOrdenado = ordena($this->getIdApuestaTipo());
            if ($idApuestaTipoOrdenado === false || $idApuestaTipoOrdenado <= 0) {
                throw new Exception("Failed to process the apuesta tipo ID for insert: " . $this->getIdApuestaTipo());
            }
        }

        $idPartidoOrdenado = null;
        if ($this->getIdPartido() !== null) {
            $idPartidoOrdenado = ordena($this->getIdPartido());
            if ($idPartidoOrdenado === false || $idPartidoOrdenado <= 0) {
                throw new Exception("Failed to process the partido ID for insert: " . $this->getIdPartido());
            }
        }

        $statement->bindValue(':id_partido_bet', $idPartidoBetOrdenado, PDO::PARAM_INT);
        $statement->bindValue(':id_apuesta_tipo', $idApuestaTipoOrdenado, PDO::PARAM_INT);
        $statement->bindValue(':cuota', $this->getCuota(), PDO::PARAM_STR);
        $statement->bindValue(':id_partido', $idPartidoOrdenado, PDO::PARAM_INT);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID and convert it to 'desordenado' format
            $lastInsertId = $conexion->lastInsertId();
            $desordenadoId = desordena($lastInsertId);
            if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                error_log("desordena() returned empty/null for inserted ID: " . $lastInsertId);
                throw new Exception("Error processing inserted PartidoBetDetalle ID. desordena() failed.");
            }
            $this->setId($desordenadoId);
            error_log("PartidoBetDetalle inserted successfully with ID: " . $this->getId());
        } else {
            error_log("Failed to insert PartidoBetDetalle: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current PartidoBetDetalle instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update PartidoBetDetalle without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
        if ($idOrdenado === false || $idOrdenado <= 0) {
            throw new Exception("Failed to process the PartidoBetDetalle ID for update: " . $this->getId());
        }

        $query = <<<SQL
        UPDATE partidos_bets_detalles SET
             id_partido_bet = :id_partido_bet
            ,id_apuesta_tipo = :id_apuesta_tipo
            ,cuota = :cuota
            ,id_partido = :id_partido
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        // Convert all foreign key IDs to ordenado for database storage
        $idPartidoBetOrdenado = null;
        if ($this->getIdPartidoBet() !== null) {
            $idPartidoBetOrdenado = ordena($this->getIdPartidoBet());
            if ($idPartidoBetOrdenado === false || $idPartidoBetOrdenado <= 0) {
                throw new Exception("Failed to process the partido bet ID for update: " . $this->getIdPartidoBet());
            }
        }

        $idApuestaTipoOrdenado = null;
        if ($this->getIdApuestaTipo() !== null) {
            $idApuestaTipoOrdenado = ordena($this->getIdApuestaTipo());
            if ($idApuestaTipoOrdenado === false || $idApuestaTipoOrdenado <= 0) {
                throw new Exception("Failed to process the apuesta tipo ID for update: " . $this->getIdApuestaTipo());
            }
        }

        $idPartidoOrdenado = null;
        if ($this->getIdPartido() !== null) {
            $idPartidoOrdenado = ordena($this->getIdPartido());
            if ($idPartidoOrdenado === false || $idPartidoOrdenado <= 0) {
                throw new Exception("Failed to process the partido ID for update: " . $this->getIdPartido());
            }
        }

        $statement->bindValue(':id_partido_bet', $idPartidoBetOrdenado, PDO::PARAM_INT);
        $statement->bindValue(':id_apuesta_tipo', $idApuestaTipoOrdenado, PDO::PARAM_INT);
        $statement->bindValue(':cuota', $this->getCuota(), PDO::PARAM_STR);
        $statement->bindValue(':id_partido', $idPartidoOrdenado, PDO::PARAM_INT);
        $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT); // Bind the 'ordenado' ID

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update PartidoBetDetalle: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Deletes (hard deletes) a PartidoBetDetalle record from the database.
     * Note: This table does not have an estado field, so this performs a hard deletion.
     *
     * @param string $id The 'desordenado' string ID of the PartidoBetDetalle to delete.
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            DELETE FROM partidos_bets_detalles
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Database error while deleting PartidoBetDetalle: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the PartidoBetDetalle.
     * Assumes global validation/formatting functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio') || !function_exists('format_numberclean')) {
            throw new Exception("Required global validation/formatting functions are missing.");
        }

        try {
            // Validate id_partido_bet (allow NULL for insert operations as per betting workflow requirements)
            // Note: id_partido_bet can be NULL when creating standalone betting records
            // Only validate if it's not null and not empty
            if ($this->getIdPartidoBet() !== null && empty(trim($this->getIdPartidoBet()))) {
                throw new Exception('El ID del partido bet no puede estar vacío si se especifica');
            }

            // Validate id_apuesta_tipo
            if ($this->getIdApuestaTipo() === null || empty(trim($this->getIdApuestaTipo()))) {
                throw new Exception('Debe especificar el ID del tipo de apuesta');
            }

            // Validate id_partido
            if ($this->getIdPartido() === null || empty(trim($this->getIdPartido()))) {
                throw new Exception('Debe especificar el ID del partido');
            }

            // Validate cuota
            $cuotaOriginal = $this->getCuota();
            if ($cuotaOriginal === null) {
                throw new Exception('Debe especificar la cuota');
            }

            $cuotaLimpia = format_numberclean((string)$cuotaOriginal);
            if (!is_numeric($cuotaLimpia)) {
                throw new Exception("La cuota proporcionada no es numérica después de la limpieza.");
            }
            if ((float)$cuotaLimpia <= 0) {
                throw new Exception("La cuota debe ser mayor que cero.");
            }
            $this->setCuota((float)$cuotaLimpia);

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIdPartidoBet(): ?string
    {
        return $this->id_partido_bet;
    }

    public function setIdPartidoBet(?string $id_partido_bet): self
    {
        $this->id_partido_bet = $id_partido_bet;
        return $this;
    }

    public function getIdApuestaTipo(): ?string
    {
        return $this->id_apuesta_tipo;
    }

    public function setIdApuestaTipo(?string $id_apuesta_tipo): self
    {
        $this->id_apuesta_tipo = $id_apuesta_tipo;
        return $this;
    }

    public function getCuota(): ?float
    {
        return $this->cuota;
    }

    public function setCuota(?float $cuota): self
    {
        $this->cuota = $cuota;
        return $this;
    }

    public function getIdPartido(): ?string
    {
        return $this->id_partido;
    }

    public function setIdPartido(?string $id_partido): self
    {
        $this->id_partido = $id_partido;
        return $this;
    }
}
