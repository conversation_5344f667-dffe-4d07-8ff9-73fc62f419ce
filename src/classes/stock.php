<?php

require_once __ROOT__ . '/src/classes/stocksector.php';
require_once __ROOT__ . '/src/classes/stockindustria.php';

class Stock
{
    public string $id;
    public string $ticker;
    public string $earnings;
    public StockSector $sector;
    public StockIndustria $industria;
    public int $is_price_week_chg_negative;
    public int $rs_rating_1w;
    public int $rs_rating_1m;
    public int $rs_rating_3m;
    public int $ad_rating;
    public int $as_rating_1w;
    public int $as_rating_1m;
    public int $as_rating_3m;
    public string $fundamental_rank;
    public int $eps_rating;
    public int $sales_rating;
    public int $eps_growth_3qago;
    public int $eps_growth_2qago;
    public int $eps_growth_last;
    public int $eps_growth_avg;
    public int $eps_surprise_3qago;
    public int $eps_surprise_2qago;
    public int $eps_surprise_last;
    public int $eps_accelerated_last3q;
    public int $eps_accelerated_last2q;
    public int $sales_growth_3qago;
    public int $sales_growth_2qago;
    public int $sales_growth_last;
    public int $sales_growth_avg;
    public int $sales_surprise_3qago;
    public int $sales_surprise_2qago;
    public int $sales_surprise_last;
    public int $sales_accelerated_last3q;
    public int $sales_accelerated_last2q;
    public int $base;
    public int $t1;
    public int $t2;
    public int $t3;
    public string $ultimo_tx;
    public int $porc_ultimo_tx;
    public int $puntos;
    public string $fecha_actualizado;    
    public int $fecha_actualizado_dias;    
    public int $focus;
    public int $standby;
    public int $comprado;
    public int $broke_out;
    public int $estado;
    public string $earnings_dias;
    public string $ultimo_tx_bg;
    public string $bd_table                      = 'stocks';
    public string $bd_alias                      = 'sto';
    public string $bd_id                         = 'id';
    public string $bd_ticker                     = 'ticker';
    public string $bd_earnings                   = 'earnings';
    public string $bd_id_sector                  = 'id_sector';
    public string $bd_f_sector_nombre            = 'nombre_sector';
    public string $bd_id_industria               = 'id_industria';
    public string $bd_f_industria_nombre         = 'nombre_industria';
    public string $bd_is_price_week_chg_negative = 'is_price_week_chg_negative';
    public string $bd_rs_rating_1w               = 'rs_rating_1w';
    public string $bd_rs_rating_1m               = 'rs_rating_1m';
    public string $bd_rs_rating_3m               = 'rs_rating_3m';
    public string $bd_ad_rating                  = 'ad_rating';
    public string $bd_as_rating_1w               = 'as_rating_1w';
    public string $bd_as_rating_1m               = 'as_rating_1m';
    public string $bd_as_rating_3m               = 'as_rating_3m';
    public string $bd_fundamental_rank           = 'fundamental_rank';
    public string $bd_eps_rating                 = 'eps_rating';
    public string $bd_sales_rating               = 'sales_rating';
    public string $bd_eps_growth_3qago           = 'eps_growth_3qago';
    public string $bd_eps_growth_2qago           = 'eps_growth_2qago';
    public string $bd_eps_growth_last            = 'eps_growth_last';
    public string $bd_eps_growth_avg             = 'eps_growth_avg';
    public string $bd_eps_surprise_3qago         = 'eps_surprise_3qago';
    public string $bd_eps_surprise_2qago         = 'eps_surprise_2qago';
    public string $bd_eps_surprise_last          = 'eps_surprise_last';
    public string $bd_eps_accelerated_last3q     = 'eps_accelerated_last3q';
    public string $bd_eps_accelerated_last2q     = 'eps_accelerated_last2q';
    public string $bd_sales_growth_3qago         = 'sales_growth_3qago';
    public string $bd_sales_growth_2qago         = 'sales_growth_2qago';
    public string $bd_sales_growth_last          = 'sales_growth_last';
    public string $bd_sales_growth_avg           = 'sales_growth_avg';
    public string $bd_sales_surprise_3qago       = 'sales_surprise_3qago';
    public string $bd_sales_surprise_2qago       = 'sales_surprise_2qago';
    public string $bd_sales_surprise_last        = 'sales_surprise_last';
    public string $bd_sales_accelerated_last3q   = 'sales_accelerated_last3q';
    public string $bd_sales_accelerated_last2q   = 'sales_accelerated_last2q';
    public string $bd_base                       = 'base';
    public string $bd_t1                         = 't1';
    public string $bd_t2                         = 't2';
    public string $bd_t3                         = 't3';
    public string $bd_puntos                     = 'puntos';
    public string $bd_fecha_actualizado          = 'fecha_actualizado';
    public string $bd_focus                      = 'focus';
    public string $bd_standby                    = 'standby';
    public string $bd_comprado                   = 'comprado';
    public string $bd_broke_out                  = 'broke_out';
    public string $bd_estado                     = 'estado';
	const MINIMO_ULTIMO_TX = 8;

    function __construct()
    {
        $this->id                         = '';
        $this->ticker                     = '';
        $this->earnings                   = '';
        $this->sector                     = new StockSector();
        $this->industria                  = new StockIndustria();
        $this->is_price_week_chg_negative = 0;
        $this->rs_rating_1w               = 0;
        $this->rs_rating_1m               = 0;
        $this->rs_rating_3m               = 0;
        $this->ad_rating                  = 0;
        $this->as_rating_1w               = 0;
        $this->as_rating_1m               = 0;
        $this->as_rating_3m               = 0;
        $this->fundamental_rank           = '';
        $this->eps_rating                 = 0;
        $this->sales_rating               = 0;
        $this->eps_growth_3qago           = 0;
        $this->eps_growth_2qago           = 0;
        $this->eps_growth_last            = 0;
        $this->eps_growth_avg             = 0;
        $this->eps_surprise_3qago         = 0;
        $this->eps_surprise_2qago         = 0;
        $this->eps_surprise_last          = 0;
        $this->eps_accelerated_last3q     = 0;
        $this->eps_accelerated_last2q     = 0;
        $this->sales_growth_3qago         = 0;
        $this->sales_growth_2qago         = 0;
        $this->sales_growth_last          = 0;
        $this->sales_growth_avg           = 0;
        $this->sales_surprise_3qago       = 0;
        $this->sales_surprise_2qago       = 0;
        $this->sales_surprise_last        = 0;
        $this->sales_accelerated_last3q   = 0;
        $this->sales_accelerated_last2q   = 0;
        $this->base                       = 0;
        $this->t1                         = 0;
        $this->t2                         = 0;
        $this->t3                         = 0;
        $this->ultimo_tx                  = '';
        $this->porc_ultimo_tx             = 0;
        $this->puntos                     = 0;
        $this->fecha_actualizado          = '';
        $this->fecha_actualizado_dias     = 0;
        $this->focus                      = 0;
        $this->standby                    = 0;
        $this->comprado                   = 0;
        $this->broke_out                  = 0;
        $this->estado                     = 0;
        $this->earnings_dias              = 0;
        $this->ultimo_tx_bg                     = '';
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq           = new self;
            $fecha_actual = create_date();

            $objeto                             = new self;
            $objeto->id                         = desordena($resultado[$cq->bd_id]);
            $objeto->ticker                     = $resultado[$cq->bd_ticker];
            $objeto->earnings                   = $resultado[$cq->bd_earnings];
            $objeto->sector->id                 = desordena($resultado[$cq->bd_id_sector]);
            $objeto->sector->nombre             = (isset($resultado["$cq->bd_f_sector_nombre"])) ? $resultado["$cq->bd_f_sector_nombre"] : "";
            $objeto->industria->id              = desordena($resultado[$cq->bd_id_industria]);
            $objeto->industria->nombre          = (isset($resultado["$cq->bd_f_industria_nombre"])) ? $resultado["$cq->bd_f_industria_nombre"] : "";
            $objeto->is_price_week_chg_negative = $resultado[$cq->bd_is_price_week_chg_negative];
            $objeto->rs_rating_1w               = $resultado[$cq->bd_rs_rating_1w];
            $objeto->rs_rating_1m               = $resultado[$cq->bd_rs_rating_1m];
            $objeto->rs_rating_3m               = $resultado[$cq->bd_rs_rating_3m];
            $objeto->ad_rating                  = $resultado[$cq->bd_ad_rating];
            $objeto->as_rating_1w               = $resultado[$cq->bd_as_rating_1w];
            $objeto->as_rating_1m               = $resultado[$cq->bd_as_rating_1m];
            $objeto->as_rating_3m               = $resultado[$cq->bd_as_rating_3m];
            $objeto->fundamental_rank           = $resultado[$cq->bd_fundamental_rank];
            $objeto->eps_rating                 = $resultado[$cq->bd_eps_rating];
            $objeto->sales_rating               = $resultado[$cq->bd_sales_rating];
            $objeto->eps_growth_3qago           = $resultado[$cq->bd_eps_growth_3qago];
            $objeto->eps_growth_2qago           = $resultado[$cq->bd_eps_growth_2qago];
            $objeto->eps_growth_last            = $resultado[$cq->bd_eps_growth_last];
            $objeto->eps_growth_avg             = $resultado[$cq->bd_eps_growth_avg];
            $objeto->eps_surprise_3qago         = $resultado[$cq->bd_eps_surprise_3qago];
            $objeto->eps_surprise_2qago         = $resultado[$cq->bd_eps_surprise_2qago];
            $objeto->eps_surprise_last          = $resultado[$cq->bd_eps_surprise_last];
            $objeto->eps_accelerated_last3q     = $resultado[$cq->bd_eps_accelerated_last3q];
            $objeto->eps_accelerated_last2q     = $resultado[$cq->bd_eps_accelerated_last2q];
            $objeto->sales_growth_3qago         = $resultado[$cq->bd_sales_growth_3qago];
            $objeto->sales_growth_2qago         = $resultado[$cq->bd_sales_growth_2qago];
            $objeto->sales_growth_last          = $resultado[$cq->bd_sales_growth_last];
            $objeto->sales_growth_avg           = $resultado[$cq->bd_sales_growth_avg];
            $objeto->sales_surprise_3qago       = $resultado[$cq->bd_sales_surprise_3qago];
            $objeto->sales_surprise_2qago       = $resultado[$cq->bd_sales_surprise_2qago];
            $objeto->sales_surprise_last        = $resultado[$cq->bd_sales_surprise_last];
            $objeto->sales_accelerated_last3q   = $resultado[$cq->bd_sales_accelerated_last3q];
            $objeto->sales_accelerated_last2q   = $resultado[$cq->bd_sales_accelerated_last2q];
            $objeto->base                       = $resultado[$cq->bd_base];
            $objeto->t1                         = $resultado[$cq->bd_t1];
            $objeto->t2                         = $resultado[$cq->bd_t2];
            $objeto->t3                         = $resultado[$cq->bd_t3];
            $objeto->puntos                     = $resultado[$cq->bd_puntos];
            $objeto->fecha_actualizado          = $resultado[$cq->bd_fecha_actualizado];
            $objeto->focus                      = $resultado[$cq->bd_focus];
            $objeto->standby                    = $resultado[$cq->bd_standby];
            $objeto->comprado                   = $resultado[$cq->bd_comprado];
            $objeto->broke_out                  = $resultado[$cq->bd_broke_out];
            $objeto->estado                     = $resultado[$cq->bd_estado];
			$objeto->ultimo_tx                  = $objeto->calcular_ultimo_tx();
			$objeto->porc_ultimo_tx             = $objeto->calcular_porc_ultimo_tx();

            if($objeto->earnings < $fecha_actual){
                $objeto->earnings_dias = 'N/A';

            } else{
                $objeto->earnings_dias = getDateDiffDays($fecha_actual, $objeto->earnings);
            }
            
            $objeto->fecha_actualizado_dias     = getDateDiffDays($fecha_actual, $objeto->fecha_actualizado);

            if($objeto->porc_ultimo_tx > 0 && $objeto->porc_ultimo_tx <= self::MINIMO_ULTIMO_TX){
                $objeto->ultimo_tx_bg = 'bg-teal-700';
            }

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($paramref, $conexion): self
    {
        try {
            $id     = (isset($paramref["id"])) ? $paramref["id"] : "";
            $ticker = (isset($paramref["ticker"])) ? $paramref["ticker"] : "";

            $cq       = new self;
            $cqa      = $cq->bd_alias;
            $cq_sec   = new StockSector();
            $cq_sec_a = $cq_sec->bd_alias;
            $cq_ind   = new StockIndustria();
            $cq_ind_a = $cq_ind->bd_alias;

            $query = "SELECT ";
            $query .= "   $cqa.* ";
            $query .= "  ,$cq_sec_a.$cq_sec->bd_nombre $cq->bd_f_sector_nombre ";
            $query .= "  ,$cq_ind_a.$cq_ind->bd_nombre $cq->bd_f_industria_nombre ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "INNER JOIN $cq_sec->bd_table $cq_sec_a ";
            $query .= "  ON ($cq_sec_a.$cq_sec->bd_id = $cqa.$cq->bd_id_sector) ";
            $query .= "INNER JOIN $cq_ind->bd_table $cq_ind_a ";
            $query .= "  ON ($cq_ind_a.$cq_ind->bd_id = $cqa.$cq->bd_id_industria) ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id > 0 ";

            if(!empty($id)){
                $query .= "AND $cqa.$cq->bd_id = :$cq->bd_id ";
            }
            if(!empty($ticker)){
                $query .= "AND $cqa.$cq->bd_ticker = :$cq->bd_ticker ";
            }

            $statement = $conexion->prepare($query);

            if(!empty($id)){
                $statement->bindValue(":$cq->bd_id", ordena($id));
            }
            if(!empty($ticker)){
                $statement->bindValue(":$cq->bd_ticker", $ticker);
            }
            
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list($paramref, $conexion): array
    {
        try {
            $id_sector = (isset($paramref["id_sector"])) ? $paramref["id_sector"] : "";
            $not_id    = (isset($paramref["not_id"])) ? $paramref["not_id"] : "";
            $ticker    = (isset($paramref["ticker"])) ? $paramref["ticker"] : "";

            $cq       = new self;
            $cqa      = $cq->bd_alias;
            $cq_sec   = new StockSector();
            $cq_sec_a = $cq_sec->bd_alias;
            $cq_ind   = new StockIndustria();
            $cq_ind_a = $cq_ind->bd_alias;

            $query = "SELECT ";
            $query .= "   $cqa.* ";
            $query .= "  ,$cq_sec_a.$cq_sec->bd_nombre $cq->bd_f_sector_nombre ";
            $query .= "  ,$cq_ind_a.$cq_ind->bd_nombre $cq->bd_f_industria_nombre ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "INNER JOIN $cq_sec->bd_table $cq_sec_a ";
            $query .= "  ON ($cq_sec_a.$cq_sec->bd_id = $cqa.$cq->bd_id_sector) ";
            $query .= "INNER JOIN $cq_ind->bd_table $cq_ind_a ";
            $query .= "  ON ($cq_ind_a.$cq_ind->bd_id = $cqa.$cq->bd_id_industria) ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = 1 ";

            if(!empty($id_sector)){
                $query .= "AND $cqa.$cq->bd_id_sector = :$cq->bd_id_sector ";
            }
            if(!empty($ticker)){
                $query .= "AND $cqa.$cq->bd_ticker = :$cq->bd_ticker ";
            }
            if(!empty($not_id)){
                $query .= "AND $cqa.$cq->bd_id <> :$cq->bd_id ";
            }

            $query .= "ORDER BY ";
            $query .= "  $cqa.$cq->bd_puntos DESC ";

            $statement = $conexion->prepare($query);

            if(!empty($id_sector)){
                $statement->bindValue(":$cq->bd_id_sector", ordena($id_sector));
            }
            if(!empty($ticker)){
                $statement->bindValue(":$cq->bd_ticker", $ticker);
            }
            if(!empty($not_id)){
                $statement->bindValue(":$cq->bd_id", ordena($not_id));
            }

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function agregar(PDO $conexion): void
    {
        try {
            $this->validate_data($conexion);
            $this->validate_data_agregar($conexion);
            $this->calcular_puntos();	
            $this->calcular_averages();	

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "   $cq->bd_ticker ";
            $query .= "  ,$cq->bd_earnings ";
            $query .= "  ,$cq->bd_id_sector ";
            $query .= "  ,$cq->bd_id_industria ";
            $query .= "  ,$cq->bd_is_price_week_chg_negative ";
            $query .= "  ,$cq->bd_rs_rating_1w ";
            $query .= "  ,$cq->bd_rs_rating_1m ";
            $query .= "  ,$cq->bd_rs_rating_3m ";
            $query .= "  ,$cq->bd_ad_rating ";
            $query .= "  ,$cq->bd_as_rating_1w ";
            $query .= "  ,$cq->bd_as_rating_1m ";
            $query .= "  ,$cq->bd_as_rating_3m ";
            $query .= "  ,$cq->bd_fundamental_rank ";
            $query .= "  ,$cq->bd_eps_rating ";
            $query .= "  ,$cq->bd_sales_rating ";
            $query .= "  ,$cq->bd_eps_growth_3qago ";
            $query .= "  ,$cq->bd_eps_growth_2qago ";
            $query .= "  ,$cq->bd_eps_growth_last ";
            $query .= "  ,$cq->bd_eps_growth_avg ";
            $query .= "  ,$cq->bd_eps_surprise_3qago ";
            $query .= "  ,$cq->bd_eps_surprise_2qago ";
            $query .= "  ,$cq->bd_eps_surprise_last ";
            $query .= "  ,$cq->bd_eps_accelerated_last3q ";
            $query .= "  ,$cq->bd_eps_accelerated_last2q ";
            $query .= "  ,$cq->bd_sales_growth_3qago ";
            $query .= "  ,$cq->bd_sales_growth_2qago ";
            $query .= "  ,$cq->bd_sales_growth_last ";
            $query .= "  ,$cq->bd_sales_growth_avg ";
            $query .= "  ,$cq->bd_sales_surprise_3qago ";
            $query .= "  ,$cq->bd_sales_surprise_2qago ";
            $query .= "  ,$cq->bd_sales_surprise_last ";
            $query .= "  ,$cq->bd_sales_accelerated_last3q ";
            $query .= "  ,$cq->bd_sales_accelerated_last2q ";
            $query .= "  ,$cq->bd_base ";
            $query .= "  ,$cq->bd_t1 ";
            $query .= "  ,$cq->bd_t2 ";
            $query .= "  ,$cq->bd_t3 ";
            $query .= "  ,$cq->bd_puntos ";
            $query .= "  ,$cq->bd_fecha_actualizado ";
            $query .= "  ,$cq->bd_broke_out ";
            $query .= ") VALUES (";
            $query .= "   :$cq->bd_ticker ";
            $query .= "  ,:$cq->bd_earnings ";
            $query .= "  ,:$cq->bd_id_sector ";
            $query .= "  ,:$cq->bd_id_industria ";
            $query .= "  ,:$cq->bd_is_price_week_chg_negative ";
            $query .= "  ,:$cq->bd_rs_rating_1w ";
            $query .= "  ,:$cq->bd_rs_rating_1m ";
            $query .= "  ,:$cq->bd_rs_rating_3m ";
            $query .= "  ,:$cq->bd_ad_rating ";
            $query .= "  ,:$cq->bd_as_rating_1w ";
            $query .= "  ,:$cq->bd_as_rating_1m ";
            $query .= "  ,:$cq->bd_as_rating_3m ";
            $query .= "  ,:$cq->bd_fundamental_rank ";
            $query .= "  ,:$cq->bd_eps_rating ";
            $query .= "  ,:$cq->bd_sales_rating ";
            $query .= "  ,:$cq->bd_eps_growth_3qago ";
            $query .= "  ,:$cq->bd_eps_growth_2qago ";
            $query .= "  ,:$cq->bd_eps_growth_last ";
            $query .= "  ,:$cq->bd_eps_growth_avg ";
            $query .= "  ,:$cq->bd_eps_surprise_3qago ";
            $query .= "  ,:$cq->bd_eps_surprise_2qago ";
            $query .= "  ,:$cq->bd_eps_surprise_last ";
            $query .= "  ,:$cq->bd_eps_accelerated_last3q ";
            $query .= "  ,:$cq->bd_eps_accelerated_last2q ";
            $query .= "  ,:$cq->bd_sales_growth_3qago ";
            $query .= "  ,:$cq->bd_sales_growth_2qago ";
            $query .= "  ,:$cq->bd_sales_growth_last ";
            $query .= "  ,:$cq->bd_sales_growth_avg ";
            $query .= "  ,:$cq->bd_sales_surprise_3qago ";
            $query .= "  ,:$cq->bd_sales_surprise_2qago ";
            $query .= "  ,:$cq->bd_sales_surprise_last ";
            $query .= "  ,:$cq->bd_sales_accelerated_last3q ";
            $query .= "  ,:$cq->bd_sales_accelerated_last2q ";
            $query .= "  ,:$cq->bd_base ";
            $query .= "  ,:$cq->bd_t1 ";
            $query .= "  ,:$cq->bd_t2 ";
            $query .= "  ,:$cq->bd_t3 ";
            $query .= "  ,:$cq->bd_puntos ";
            $query .= "  ,:$cq->bd_fecha_actualizado ";
            $query .= "  ,:$cq->bd_broke_out ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_ticker", $this->ticker);
            $statement->bindValue(":$cq->bd_earnings", $this->earnings);
            $statement->bindValue(":$cq->bd_id_sector", ordena($this->sector->id));
            $statement->bindValue(":$cq->bd_id_industria", ordena($this->industria->id));
            $statement->bindValue(":$cq->bd_is_price_week_chg_negative", $this->is_price_week_chg_negative);
            $statement->bindValue(":$cq->bd_rs_rating_1w", $this->rs_rating_1w);
            $statement->bindValue(":$cq->bd_rs_rating_1m", $this->rs_rating_1m);
            $statement->bindValue(":$cq->bd_rs_rating_3m", $this->rs_rating_3m);
            $statement->bindValue(":$cq->bd_ad_rating", $this->ad_rating);
            $statement->bindValue(":$cq->bd_as_rating_1w", $this->as_rating_1w);
            $statement->bindValue(":$cq->bd_as_rating_1m", $this->as_rating_1m);
            $statement->bindValue(":$cq->bd_as_rating_3m", $this->as_rating_3m);
            $statement->bindValue(":$cq->bd_fundamental_rank", $this->fundamental_rank);
            $statement->bindValue(":$cq->bd_eps_rating", $this->eps_rating);
            $statement->bindValue(":$cq->bd_sales_rating", $this->sales_rating);
            $statement->bindValue(":$cq->bd_eps_growth_3qago", $this->eps_growth_3qago);
            $statement->bindValue(":$cq->bd_eps_growth_2qago", $this->eps_growth_2qago);
            $statement->bindValue(":$cq->bd_eps_growth_last", $this->eps_growth_last);
            $statement->bindValue(":$cq->bd_eps_growth_avg", $this->eps_growth_avg);
            $statement->bindValue(":$cq->bd_eps_surprise_3qago", $this->eps_surprise_3qago);
            $statement->bindValue(":$cq->bd_eps_surprise_2qago", $this->eps_surprise_2qago);
            $statement->bindValue(":$cq->bd_eps_surprise_last", $this->eps_surprise_last);
            $statement->bindValue(":$cq->bd_eps_accelerated_last3q", $this->eps_accelerated_last3q);
            $statement->bindValue(":$cq->bd_eps_accelerated_last2q", $this->eps_accelerated_last2q);
            $statement->bindValue(":$cq->bd_sales_growth_3qago", $this->sales_growth_3qago);
            $statement->bindValue(":$cq->bd_sales_growth_2qago", $this->sales_growth_2qago);
            $statement->bindValue(":$cq->bd_sales_growth_last", $this->sales_growth_last);
            $statement->bindValue(":$cq->bd_sales_growth_avg", $this->sales_growth_avg);
            $statement->bindValue(":$cq->bd_sales_surprise_3qago", $this->sales_surprise_3qago);
            $statement->bindValue(":$cq->bd_sales_surprise_2qago", $this->sales_surprise_2qago);
            $statement->bindValue(":$cq->bd_sales_surprise_last", $this->sales_surprise_last);
            $statement->bindValue(":$cq->bd_sales_accelerated_last3q", $this->sales_accelerated_last3q);
            $statement->bindValue(":$cq->bd_sales_accelerated_last2q", $this->sales_accelerated_last2q);
            $statement->bindValue(":$cq->bd_base", $this->base);
            $statement->bindValue(":$cq->bd_t1", $this->t1);
            $statement->bindValue(":$cq->bd_t2", $this->t2);
            $statement->bindValue(":$cq->bd_t3", $this->t3);
            $statement->bindValue(":$cq->bd_puntos", $this->puntos);
            $statement->bindValue(":$cq->bd_fecha_actualizado", create_date());
            $statement->bindValue(":$cq->bd_broke_out", $this->broke_out);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modificar(PDO $conexion): void
    {
        try {
            $this->validate_data($conexion);
            $this->validate_data_modificar($conexion);
            $this->calcular_puntos();
            $this->calcular_averages();

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "   $cq->bd_earnings = :$cq->bd_earnings ";
            $query .= "  ,$cq->bd_id_sector = :$cq->bd_id_sector ";
            $query .= "  ,$cq->bd_id_industria = :$cq->bd_id_industria ";
            $query .= "  ,$cq->bd_is_price_week_chg_negative = :$cq->bd_is_price_week_chg_negative ";
            $query .= "  ,$cq->bd_rs_rating_1w = :$cq->bd_rs_rating_1w ";
            $query .= "  ,$cq->bd_rs_rating_1m = :$cq->bd_rs_rating_1m ";
            $query .= "  ,$cq->bd_rs_rating_3m = :$cq->bd_rs_rating_3m ";
            $query .= "  ,$cq->bd_ad_rating = :$cq->bd_ad_rating ";
            $query .= "  ,$cq->bd_as_rating_1w = :$cq->bd_as_rating_1w ";
            $query .= "  ,$cq->bd_as_rating_1m = :$cq->bd_as_rating_1m ";
            $query .= "  ,$cq->bd_as_rating_3m = :$cq->bd_as_rating_3m ";
            $query .= "  ,$cq->bd_fundamental_rank = :$cq->bd_fundamental_rank ";
            $query .= "  ,$cq->bd_eps_rating = :$cq->bd_eps_rating ";
            $query .= "  ,$cq->bd_sales_rating = :$cq->bd_sales_rating ";
            $query .= "  ,$cq->bd_eps_growth_3qago = :$cq->bd_eps_growth_3qago ";
            $query .= "  ,$cq->bd_eps_growth_2qago = :$cq->bd_eps_growth_2qago ";
            $query .= "  ,$cq->bd_eps_growth_last = :$cq->bd_eps_growth_last ";
            $query .= "  ,$cq->bd_eps_growth_avg = :$cq->bd_eps_growth_avg ";
            $query .= "  ,$cq->bd_eps_surprise_3qago = :$cq->bd_eps_surprise_3qago ";
            $query .= "  ,$cq->bd_eps_surprise_2qago = :$cq->bd_eps_surprise_2qago ";
            $query .= "  ,$cq->bd_eps_surprise_last = :$cq->bd_eps_surprise_last ";
            $query .= "  ,$cq->bd_eps_accelerated_last3q = :$cq->bd_eps_accelerated_last3q ";
            $query .= "  ,$cq->bd_eps_accelerated_last2q = :$cq->bd_eps_accelerated_last2q ";
            $query .= "  ,$cq->bd_sales_growth_3qago = :$cq->bd_sales_growth_3qago ";
            $query .= "  ,$cq->bd_sales_growth_2qago = :$cq->bd_sales_growth_2qago ";
            $query .= "  ,$cq->bd_sales_growth_last = :$cq->bd_sales_growth_last ";
            $query .= "  ,$cq->bd_sales_growth_avg = :$cq->bd_sales_growth_avg ";
            $query .= "  ,$cq->bd_sales_surprise_3qago = :$cq->bd_sales_surprise_3qago ";
            $query .= "  ,$cq->bd_sales_surprise_2qago = :$cq->bd_sales_surprise_2qago ";
            $query .= "  ,$cq->bd_sales_surprise_last = :$cq->bd_sales_surprise_last ";
            $query .= "  ,$cq->bd_sales_accelerated_last3q = :$cq->bd_sales_accelerated_last3q ";
            $query .= "  ,$cq->bd_sales_accelerated_last2q = :$cq->bd_sales_accelerated_last2q ";
            $query .= "  ,$cq->bd_base = :$cq->bd_base ";
            $query .= "  ,$cq->bd_t1 = :$cq->bd_t1 ";
            $query .= "  ,$cq->bd_t2 = :$cq->bd_t2 ";
            $query .= "  ,$cq->bd_t3 = :$cq->bd_t3 ";
            $query .= "  ,$cq->bd_puntos = :$cq->bd_puntos ";
            $query .= "  ,$cq->bd_fecha_actualizado = :$cq->bd_fecha_actualizado ";
            $query .= "  ,$cq->bd_broke_out = :$cq->bd_broke_out ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_earnings", $this->earnings);
            $statement->bindValue(":$cq->bd_id_sector", ordena($this->sector->id));
            $statement->bindValue(":$cq->bd_id_industria", ordena($this->industria->id));
            $statement->bindValue(":$cq->bd_is_price_week_chg_negative", $this->is_price_week_chg_negative);
            $statement->bindValue(":$cq->bd_rs_rating_1w", $this->rs_rating_1w);
            $statement->bindValue(":$cq->bd_rs_rating_1m", $this->rs_rating_1m);
            $statement->bindValue(":$cq->bd_rs_rating_3m", $this->rs_rating_3m);
            $statement->bindValue(":$cq->bd_ad_rating", $this->ad_rating);
            $statement->bindValue(":$cq->bd_as_rating_1w", $this->as_rating_1w);
            $statement->bindValue(":$cq->bd_as_rating_1m", $this->as_rating_1m);
            $statement->bindValue(":$cq->bd_as_rating_3m", $this->as_rating_3m);
            $statement->bindValue(":$cq->bd_fundamental_rank", $this->fundamental_rank);
            $statement->bindValue(":$cq->bd_eps_rating", $this->eps_rating);
            $statement->bindValue(":$cq->bd_sales_rating", $this->sales_rating);
            $statement->bindValue(":$cq->bd_eps_growth_3qago", $this->eps_growth_3qago);
            $statement->bindValue(":$cq->bd_eps_growth_2qago", $this->eps_growth_2qago);
            $statement->bindValue(":$cq->bd_eps_growth_last", $this->eps_growth_last);
            $statement->bindValue(":$cq->bd_eps_growth_avg", $this->eps_growth_avg);
            $statement->bindValue(":$cq->bd_eps_surprise_3qago", $this->eps_surprise_3qago);
            $statement->bindValue(":$cq->bd_eps_surprise_2qago", $this->eps_surprise_2qago);
            $statement->bindValue(":$cq->bd_eps_surprise_last", $this->eps_surprise_last);
            $statement->bindValue(":$cq->bd_eps_accelerated_last3q", $this->eps_accelerated_last3q);
            $statement->bindValue(":$cq->bd_eps_accelerated_last2q", $this->eps_accelerated_last2q);
            $statement->bindValue(":$cq->bd_sales_growth_3qago", $this->sales_growth_3qago);
            $statement->bindValue(":$cq->bd_sales_growth_2qago", $this->sales_growth_2qago);
            $statement->bindValue(":$cq->bd_sales_growth_last", $this->sales_growth_last);
            $statement->bindValue(":$cq->bd_sales_growth_avg", $this->sales_growth_avg);
            $statement->bindValue(":$cq->bd_sales_surprise_3qago", $this->sales_surprise_3qago);
            $statement->bindValue(":$cq->bd_sales_surprise_2qago", $this->sales_surprise_2qago);
            $statement->bindValue(":$cq->bd_sales_surprise_last", $this->sales_surprise_last);
            $statement->bindValue(":$cq->bd_sales_accelerated_last3q", $this->sales_accelerated_last3q);
            $statement->bindValue(":$cq->bd_sales_accelerated_last2q", $this->sales_accelerated_last2q);
            $statement->bindValue(":$cq->bd_base", $this->base);
            $statement->bindValue(":$cq->bd_t1", $this->t1);
            $statement->bindValue(":$cq->bd_t2", $this->t2);
            $statement->bindValue(":$cq->bd_t3", $this->t3);
            $statement->bindValue(":$cq->bd_puntos", $this->puntos);
            $statement->bindValue(":$cq->bd_fecha_actualizado", create_date());
            $statement->bindValue(":$cq->bd_broke_out", $this->broke_out);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
	
	/**
	 * @throws Exception
	 */
	public static function modificar_comprado($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_focus = 0 ";
			$query .= "  ,$cq->bd_standby = 0 ";
			$query .= "  ,$cq->bd_comprado = 1 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modificar_focus($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_focus = 1 ";
			$query .= "  ,$cq->bd_standby = 0 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function modificar_standby($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_focus = 0 ";
			$query .= "  ,$cq->bd_standby = 1 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function calcular_puntos(): void
    {
        try {
            $this->puntos = 0;

            if($this->is_price_week_chg_negative == 1){
                $this->puntos -= 2;
            }

            //BEGIN RS rating
            if($this->rs_rating_1w >= 90){
                $this->puntos += 1;
            }
            if($this->rs_rating_1w >= 70){
                $this->puntos += 1;
            }
            if($this->rs_rating_1m >= 90){
                $this->puntos += 1;
            }
            if($this->rs_rating_1m >= 70){
                $this->puntos += 1;
            }
            if($this->rs_rating_3m >= 90){
                $this->puntos += 1;
            }
            if($this->rs_rating_3m >= 70){
                $this->puntos += 1;
            }
            //END RS rating

            //BEGIN AD rating
            if($this->ad_rating >= 90){
                $this->puntos += 1;
            }
            if($this->ad_rating >= 70){
                $this->puntos += 1;
            }
            if($this->ad_rating <= 0){
                $this->puntos -= 2;
            }
            //END AD rating

            //BEGIN AS rating
            if($this->as_rating_1w >= 90){
                $this->puntos += 1;
            }
            if($this->as_rating_1w >= 70){
                $this->puntos += 1;
            }
            if($this->as_rating_1m >= 90){
                $this->puntos += 1;
            }
            if($this->as_rating_1m >= 70){
                $this->puntos += 1;
            }
            if($this->as_rating_3m >= 90){
                $this->puntos += 1;
            }
            if($this->as_rating_3m >= 70){
                $this->puntos += 1;
            }
            //END AS rating

            //BEGIN fundamental rank
            switch ($this->fundamental_rank) {
                case 'A+':
                    $this->puntos += 2;            
                    break;

                case 'A':
                    $this->puntos += 1;
                    break;
            }
            //END fundamental rank

            //BEGIN EPS
            if($this->eps_rating >= 90){
                $this->puntos += 1;
            }
            if($this->eps_rating >= 70){
                $this->puntos += 1;
            }
            if($this->eps_growth_3qago >= 20){
                $this->puntos += 1;
            }
            if($this->eps_growth_3qago >= 40){
                $this->puntos += 1;
            }
            if($this->eps_growth_2qago >= 20){
                $this->puntos += 1;
            }
            if($this->eps_growth_2qago >= 40){
                $this->puntos += 1;
            }
            if($this->eps_growth_last >= 20){
                $this->puntos += 1;
            }
            if($this->eps_growth_last >= 40){
                $this->puntos += 1;
            }

            if($this->eps_surprise_3qago <= 0){
                $this->puntos -= 1;
            } else{
                $this->puntos += 1;
            }
            if($this->eps_surprise_2qago <= 0){
                $this->puntos -= 1;
            } else{
                $this->puntos += 1;
            }
            if($this->eps_surprise_last <= 0){
                $this->puntos -= 1;
            } else{
                $this->puntos += 1;
            }

            if($this->eps_accelerated_last3q == 1){
                $this->puntos += 2;
            }
            if($this->eps_accelerated_last2q == 1){
                $this->puntos += 2;
            }
            //END EPS

            //BEGIN Sales
            if($this->sales_rating >= 90){
                $this->puntos += 1;
            }
            if($this->sales_rating >= 70){
                $this->puntos += 1;
            }
            if($this->sales_growth_3qago >= 20){
                $this->puntos += 1;
            }
            if($this->sales_growth_3qago >= 40){
                $this->puntos += 1;
            }
            if($this->sales_growth_2qago >= 20){
                $this->puntos += 1;
            }
            if($this->sales_growth_2qago >= 40){
                $this->puntos += 1;
            }
            if($this->sales_growth_last >= 20){
                $this->puntos += 1;
            }
            if($this->sales_growth_last >= 40){
                $this->puntos += 1;
            }

            if($this->sales_surprise_3qago <= 0){
                $this->puntos -= 1;
            } else{
                $this->puntos += 1;
            }
            if($this->sales_surprise_2qago <= 0){
                $this->puntos -= 1;
            } else{
                $this->puntos += 1;
            }
            if($this->sales_surprise_last <= 0){
                $this->puntos -= 1;
            } else{
                $this->puntos += 1;
            }

            if($this->sales_accelerated_last3q == 1){
                $this->puntos += 2;
            }
            if($this->sales_accelerated_last2q == 1){
                $this->puntos += 2;
            }
            //END Sales

            //BEGIN analisis tecnico
            if ($this->base > 35) {
				$this->puntos += -1;
			}
			if ($this->base <= 35) {
				$this->puntos += 1;
			}

            if ($this->t1 > 0 && $this->t1 < $this->base) {
				$this->puntos += 1;
			}
			if ($this->t2 > 0 && $this->t2 < $this->t1) {
				$this->puntos += 1;
			}
			if ($this->t3 > 0 && $this->t3 < $this->t2) {
				$this->puntos += 1;
			}
			
			$this->ultimo_tx = $this->calcular_ultimo_tx();
			
			if($this->ultimo_tx > 0 && $this->ultimo_tx <= self::MINIMO_ULTIMO_TX){
				$this->puntos += 1;
			}
            //END analisis tecnico
    
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
	
	/**
	 * @throws Exception
	 */
	private function calcular_ultimo_tx(): string
	{
	    try {
			if($this->t3 > 0){
				return 'T3';
			}
		    if($this->t2 > 0){
			    return 'T2';
		    }
		    if($this->t1 > 0){
			    return 'T1';
		    }
		    if($this->base > 0){
			    return 'BASE';
		    }
			
			return 'N/A';
	    
	    } catch (Exception $e) {
	        throw new Exception($e->getMessage());
	    }
	}
	
	/**
	 * @throws Exception
	 */
	private function calcular_porc_ultimo_tx(): float|int
	{
	    try {
			if($this->t3 > 0){
				return $this->t3;
			}
		    if($this->t2 > 0){
			    return $this->t2;
		    }
		    if($this->t1 > 0){
			    return $this->t1;
		    }
		    if($this->base > 0){
			    return $this->base;
		    }
			
			return 0;
	    
	    } catch (Exception $e) {
	        throw new Exception($e->getMessage());
	    }
	}

    /**
     * @throws Exception
     */
    private function calcular_averages(): void
    {
        try {
            $this->eps_growth_avg   = round(($this->eps_growth_3qago + $this->eps_growth_2qago + $this->eps_growth_last) / 3);
            $this->sales_growth_avg = round(($this->sales_growth_3qago + $this->sales_growth_2qago + $this->sales_growth_last) / 3);
    
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data($conexion): void
    {
        try {
            validar_textovacio($this->ticker, 'Debe especificar el ticker.');
            validar_textovacio($this->earnings, 'Debe especificar los earnings.');

            $this->ticker        = mb_strtoupper($this->ticker);
            $this->sector->id    = StockSector::get_by_nombre($this->sector->nombre, $conexion)->id;
            $this->industria->id = StockIndustria::get_by_nombre($this->industria->nombre, $conexion)->id;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data_agregar($conexion): void
    {
        try {
            $stocks = self::get_list(array('ticker' => $this->ticker), $conexion);

            if(count($stocks) > 0){
                throw new Exception("Ya existe este stock en el sistema");
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data_modificar($conexion): void
    {
        try {
            $param           = array();
            $param['not_id'] = $this->id;
            $param['ticker'] = $this->ticker;
            $stocks          = self::get_list($param, $conexion);

            if(count($stocks) > 0){
                throw new Exception("Ya existe este stock en el sistema");
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>