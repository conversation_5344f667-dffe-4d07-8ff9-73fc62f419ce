<?php


class Source
{
    public string $id;
    public string $nombre;
    public int $estado;
    public string $bd_table = 'bt_sources';
    public string $bd_alias = 'sour';
    public string $bd_id = 'id';
    public string $bd_nombre = 'nombre';
    public string $bd_estado = 'estado';

    function __construct()
    {
        $this->id     = '';
        $this->nombre = '';
        $this->estado = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto         = new self;
            $objeto->id     = desordena($resultado[$cq->bd_id]);
            $objeto->nombre = $resultado[$cq->bd_nombre];
            $objeto->estado = $resultado[$cq->bd_estado];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($paramref, $conexion): self
    {
        try {
            $nombre = (isset($paramref["nombre"])) ? $paramref["nombre"] : "";

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id > 0 ";

            if(!empty($nombre)){
                $query .= "AND $cqa.$cq->bd_nombre = :$cq->bd_nombre ";
            }

            $statement = $conexion->prepare($query);

            if(!empty($nombre)){
                $statement->bindValue(":$cq->bd_nombre", $nombre);
            }
            
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list($paramref, $conexion): array
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = 1 ";

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function agregar(PDO $conexion): void
    {
        try {
            $this->validate_data();			

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_nombre ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_nombre ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modificar(PDO $conexion): void
    {
        try {
            $this->validate_data();

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_nombre = :$cq->bd_nombre ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data(): void
    {
        try {
            validar_textovacio($this->nombre, 'Debe especificar el nombre.');

            $this->nombre = mb_strtoupper($this->nombre);

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>