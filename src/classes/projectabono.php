<?php

require_once __ROOT__ . '/src/classes/project.php';

class ProjectAbono
{
    public string $id;
    public Project $project;
    public string $fecha;
    public float $valor;
    private string $bd_table = 'projects_abonos';
    private string $bd_alias = 'proabo';
    private string $bd_id = 'id_project_abono';
    private string $bd_id_project = 'id_project';
    private string $bd_fecha = 'fecha';
    private string $bd_valor = 'valor';

    function __construct()
    {
        $this->id = '';
        $this->project = new Project();
        $this->fecha = '';
        $this->valor = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id = desordena($resultado[$cq->bd_id]);
            $objeto->project = new Project();
            $objeto->project->id_project = desordena($resultado[$cq->bd_id_project]);
            $objeto->fecha = $resultado[$cq->bd_fecha];
            $objeto->valor = $resultado[$cq->bd_valor];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, PDO $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list(array $paramref, PDO $conexion): array
    {
        try {
            $id_project = $paramref['id_project'];

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id_project = :$cq->bd_id_project ";
            $query .= "ORDER BY ";
            $query .= "  $cqa.$cq->bd_fecha DESC ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id_project", ordena($id_project));
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add(PDO $conexion): void
    {
        try {
            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "   $cq->bd_id_project ";
            $query .= "  ,$cq->bd_fecha ";
            $query .= "  ,$cq->bd_valor ";
            $query .= ") VALUES (";
            $query .= "   :$cq->bd_id_project ";
            $query .= "  ,:$cq->bd_fecha ";
            $query .= "  ,:$cq->bd_valor ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id_project", ordena($this->project->id_project));
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->bindValue(":$cq->bd_valor", $this->valor);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function delete(PDO $conexion): void
    {
        try {
            $cq = new self;

            $query = " DELETE FROM $cq->bd_table ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>