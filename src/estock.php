<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/stock.php';
require_once __ROOT__ . '/src/classes/stocksector.php';
require_once __ROOT__ . '/src/classes/stockindustria.php';
require_once __ROOT__ . '/src/general/preparar.php';

$id_stock   = '';
$mod_stock  = new Stock();
$ajax_check = 0;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['id_stock'])) {
            $id_stock = $_SESSION['id_stock'];
        
            // logic:
            $mod_stock = Stock::get(array('id' => $id_stock), $conexion);
        
            unset($_SESSION['id_stock']);
        } else {
            header('Location: lstocks');
            exit();
        }

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        if(isset($_POST['ajax_check'])){
            $ajax_check	= @limpiar_datos($_POST['ajax_check']);
        } else{
            $ajax_check = 0;
        }

        if($ajax_check == 0){
            $id_stock                              = limpiar_datos($_POST['id_stock']);
            $mod_stock->ticker                     = limpiar_datos($_POST['ticker']);
            $mod_stock->earnings                   = limpiar_datos($_POST['earnings']);
            $mod_stock->sector->nombre             = limpiar_datos($_POST['sector']);
            $mod_stock->industria->nombre          = limpiar_datos($_POST['industria']);
            $mod_stock->is_price_week_chg_negative = @getvalue_checkbox($_POST['is_price_week_chg_negative']);
            $mod_stock->rs_rating_1w               = limpiar_datos($_POST['rs_rating_1w']);
            $mod_stock->rs_rating_1m               = limpiar_datos($_POST['rs_rating_1m']);
            $mod_stock->rs_rating_3m               = limpiar_datos($_POST['rs_rating_3m']);
            $mod_stock->ad_rating                  = limpiar_datos($_POST['ad_rating']);
            $mod_stock->as_rating_1w               = limpiar_datos($_POST['as_rating_1w']);
            $mod_stock->as_rating_1m               = limpiar_datos($_POST['as_rating_1m']);
            $mod_stock->as_rating_3m               = limpiar_datos($_POST['as_rating_3m']);
            $mod_stock->fundamental_rank           = limpiar_datos($_POST['fundamental_rank']);
            $mod_stock->eps_rating                 = limpiar_datos($_POST['eps_rating']);
            $mod_stock->eps_growth_3qago           = limpiar_datos($_POST['eps_growth_3qago']);
            $mod_stock->eps_growth_2qago           = limpiar_datos($_POST['eps_growth_2qago']);
            $mod_stock->eps_growth_last            = limpiar_datos($_POST['eps_growth_last']);
            $mod_stock->eps_surprise_3qago         = limpiar_datos($_POST['eps_surprise_3qago']);
            $mod_stock->eps_surprise_2qago         = limpiar_datos($_POST['eps_surprise_2qago']);
            $mod_stock->eps_surprise_last          = limpiar_datos($_POST['eps_surprise_last']);
            $mod_stock->eps_accelerated_last3q     = @getvalue_checkbox($_POST['eps_accelerated_last3q']);
            $mod_stock->eps_accelerated_last2q     = @getvalue_checkbox($_POST['eps_accelerated_last2q']);
            $mod_stock->sales_rating               = limpiar_datos($_POST['sales_rating']);
            $mod_stock->sales_growth_3qago         = limpiar_datos($_POST['sales_growth_3qago']);
            $mod_stock->sales_growth_2qago         = limpiar_datos($_POST['sales_growth_2qago']);
            $mod_stock->sales_growth_last          = limpiar_datos($_POST['sales_growth_last']);
            $mod_stock->sales_surprise_3qago       = limpiar_datos($_POST['sales_surprise_3qago']);
            $mod_stock->sales_surprise_2qago       = limpiar_datos($_POST['sales_surprise_2qago']);
            $mod_stock->sales_surprise_last        = limpiar_datos($_POST['sales_surprise_last']);
            $mod_stock->sales_accelerated_last3q   = @getvalue_checkbox($_POST['sales_accelerated_last3q']);
            $mod_stock->sales_accelerated_last2q   = @getvalue_checkbox($_POST['sales_accelerated_last2q']);
            $mod_stock->base                       = limpiar_datos($_POST['base']);
            $mod_stock->t1                         = limpiar_datos($_POST['t1']);
            $mod_stock->t2                         = limpiar_datos($_POST['t2']);
            $mod_stock->t3                         = limpiar_datos($_POST['t3']);
            $mod_stock->broke_out                  = @getvalue_checkbox($_POST['broke_out']);
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_crear_industria
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_crear_industria'])) {
    try {
        $new_industria = new StockIndustria();
        $new_industria->nombre = limpiar_datos($_POST['crear_industria_nombre']);
        $new_industria->agregar($conexion);

        $mod_stock->industria->nombre = $new_industria->nombre;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_crear_industria
#region sub_modificar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_modificar'])) {
    try {
        $mod_stock->id = $id_stock;
        $mod_stock->modificar($conexion);   

        $_SESSION['id_sector'] = $mod_stock->sector->id;
        $_SESSION['id_stock']  = $mod_stock->id;

        header('Location: lstocks?m=1');
        exit();    

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_modificar
#region sub_marcar_standby
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_marcar_standby'])) {
    try {
        Stock::modificar_standby($id_stock, $conexion);

        $_SESSION['id_sector'] = $mod_stock->sector->id;
        $_SESSION['id_stock']  = $id_stock;

        header('Location: lstocks?m=1');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_marcar_standby
#region sub_marcar_focus
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_marcar_focus'])) {
    try {
        Stock::modificar_focus($id_stock, $conexion);

        $_SESSION['id_sector'] = $mod_stock->sector->id;
        $_SESSION['id_stock']  = $id_stock;

        header('Location: lstocks?m=1');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_marcar_focus
#region region AJAX link_crear_sector_clicked
if (isset($_POST['action']) && $_POST['action'] == 'button_crear_sector_clicked') {
    $new_sector         = new StockSector();
    $new_sector->nombre = limpiar_datos($_POST['crear_sector_nombre']);
    $new_sector->agregar($conexion);

    // Prepare the response as an associative array
    $response = array(
        'sector' => $new_sector->nombre
    );

    // Return the JSON encoded response
    header('Content-Type: application/json');
    echo json_encode($response);  
    exit();   
}
#endregion AJAX link_crear_sector_clicked
#region region AJAX link_crear_industria_clicked
if (isset($_POST['action']) && $_POST['action'] == 'button_crear_industria_clicked') {
    $new_industria         = new StockIndustria();
    $new_industria->nombre = limpiar_datos($_POST['crear_industria_nombre']);
    $new_industria->agregar($conexion);

    // Prepare the response as an associative array
    $response = array(
        'industria' => $new_industria->nombre
    );

    // Return the JSON encoded response
    header('Content-Type: application/json');
    echo json_encode($response);    
    exit();
}
#endregion AJAX link_crear_industria_clicked
#region try
try {
    $sectores   = StockSector::get_list(array(), $conexion);
    $industrias = StockIndustria::get_list(array(), $conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/estock.view.php';

?>