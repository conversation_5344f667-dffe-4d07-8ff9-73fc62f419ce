<?php session_start();
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/note.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_GET['idn'])) {
			$sel_note = Note::get($_GET['idn'], $conexion);
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion
#region sub_edit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_edit'])) {
	try {
		$id_note = limpiar_datos($_POST['id_note']);
		$title = limpiar_datos($_POST['title']);
		$detail = limpiar_datos($_POST['detail']);
		$source1ref = limpiar_datos($_POST['source1ref']);
		$source1title = limpiar_datos($_POST['source1title']);
		$source2ref = limpiar_datos($_POST['source2ref']);
		$source2title = limpiar_datos($_POST['source2title']);
		$tags = limpiar_datos($_POST['tags']);

		validar_textovacio($id_note, 'Specify id_note');
		validar_textovacio($title, 'Specify title');
		validar_textovacio($detail, 'Specify detail');

		$edit_note = new Note;
		$edit_note->id_note = $id_note;
		$edit_note->title = strtolower($title);
		$edit_note->detail = $detail;
		$edit_note->source1ref = ($source1ref == "") ? '#' : $source1ref;
		$edit_note->source1title = ($source1title == "") ? 'NA' : $source1title;
		$edit_note->source2ref = ($source2ref == "") ? '#' : $source2ref;
		$edit_note->source2title = ($source2title == "") ? 'NA' : $source2title;
		$edit_note->tags = strtolower($tags);
		$edit_note->modify($conexion);

		header('Location: lnotes?add=1&search='.$edit_note->title.'&in='.$edit_note->id_note);

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion

require_once __ROOT__ . '/views/enotes.view.php';

?>
