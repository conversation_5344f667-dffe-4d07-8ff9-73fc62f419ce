<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/btapuesta.php';
require_once __ROOT__ . '/src/classes/source.php';
require_once __ROOT__ . '/src/classes/channel.php';
require_once __ROOT__ . '/src/classes/equipo.php';
require_once __ROOT__ . '/src/classes/torneo.php';
require_once __ROOT__ . '/src/classes/stakazo.php';
require_once __ROOT__ . '/src/general/preparar.php';

$mod_btapuesta = new BtApuesta();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['id_apuesta'])) {
            $id_apuesta = $_SESSION['id_apuesta'];

            // logic:
            $mod_btapuesta = BtApuesta::get($id_apuesta, $conexion);
        
            unset($_SESSION['id_apuesta']);

        } else {
            header('Location: lapuestas');
            exit();
        }

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $mod_btapuesta->id                  = limpiar_datos($_POST['id_apuesta']);
        $mod_btapuesta->fecha               = limpiar_datos($_POST['fecha']);
        $mod_btapuesta->source->nombre      = limpiar_datos($_POST['source']);
        $mod_btapuesta->channel->nombre     = limpiar_datos($_POST['channel']);
        $mod_btapuesta->stake               = limpiar_datos($_POST['stake']);
        $mod_btapuesta->stake_source        = limpiar_datos($_POST['stake_source']);
        $mod_btapuesta->equipo_home->nombre = limpiar_datos($_POST['equipo_home']);
        $mod_btapuesta->equipo_away->nombre = limpiar_datos($_POST['equipo_away']);
        $mod_btapuesta->torneo->nombre      = limpiar_datos($_POST['torneo']);
        $mod_btapuesta->tipo_apuesta        = limpiar_datos($_POST['tipo_apuesta']);
        $mod_btapuesta->tipo_apuesta_add    = limpiar_datos($_POST['tipo_apuesta_add']);
        $mod_btapuesta->tipo_apuesta_add2   = limpiar_datos($_POST['tipo_apuesta_add2']);
        $mod_btapuesta->apostado            = limpiar_datos($_POST['apostado']);
        $mod_btapuesta->cuota_source        = limpiar_datos($_POST['cuota_source']);
        $mod_btapuesta->cuota_grupo         = limpiar_datos($_POST['cuota_grupo']);
        $mod_btapuesta->cuota_real          = limpiar_datos($_POST['cuota_real']);
        $mod_btapuesta->bankroll            = limpiar_datos($_POST['bankroll']);
        $mod_btapuesta->stakazo->nombre     = limpiar_datos($_POST['stakazo']);
        $mod_btapuesta->probabilidades      = limpiar_datos($_POST['probabilidades']);
        $mod_btapuesta->fecha_partido       = limpiar_datos($_POST['fecha_partido']);
        $mod_btapuesta->fecha_partido_hora  = limpiar_datos($_POST['fecha_partido_hora']);
        $mod_btapuesta->usado               = @getvalue_checkbox($_POST['usado']);      
        $mod_btapuesta->es_combinada        = @getvalue_checkbox($_POST['es_combinada']);      

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_modificar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_modificar'])) {
    try {
        $mod_btapuesta->modificar($conexion);

        header('Location: lapuestas?m=1');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_modificar
#region sub_addequipo_agregar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addequipo_agregar'])) {
    try {
        $new_equipo = new Equipo();
        $new_equipo->nombre = limpiar_datos($_POST['addequipo_nombre']);
        $new_equipo->agregar($conexion);

        if(empty($mod_btapuesta->equipo_home->nombre)){
            $mod_btapuesta->equipo_home->nombre = $new_equipo->nombre;
        }
        if(empty($mod_btapuesta->equipo_away->nombre)){
            $mod_btapuesta->equipo_away->nombre = $new_equipo->nombre;
        }

        $success_display = 'show';
        $success_text = 'Equipo agregado.';
        
        
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addequipo_agregar
#region sub_addtorneo_agregar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addtorneo_agregar'])) {
    try {
        $new_torneo         = new Torneo();
        $new_torneo->nombre = limpiar_datos($_POST['addtorneo_nombre']);
        $new_torneo->agregar($conexion);

        $mod_btapuesta->torneo->nombre = $new_torneo->nombre;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addtorneo_agregar
#region try
try {
    $sources  = Source::get_list(array(), $conexion);
    $channels = Channel::get_list(array(), $conexion);
    $equipos  = Equipo::get_list(array(), $conexion);
    $torneos  = Torneo::get_list(array(), $conexion);
    $stakazos = Stakazo::get_list(array(), $conexion);
    
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/eapuesta.view.php';

?>