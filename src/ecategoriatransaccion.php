<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/categoriatransaccion.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['idcategoriatransaccion'])) {
            $idcategoriatransaccion = $_SESSION['idcategoriatransaccion'];

            // logic:

            unset($_SESSION['idcategoriatransaccion']);
        } else {
            header('Location: lcategoriastransaccion');
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $idcategoriatransaccion = limpiar_datos($_POST['idcategoriatransaccion']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_mod
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_mod'])) {
    try {
        $modcateg = new CategoriaTransaccion();
        $modcateg->id = $idcategoriatransaccion;
        $modcateg->nombre = limpiar_datos($_POST['nombre']);
        $modcateg->color = limpiar_datos($_POST['color']);
        $modcateg->modify($conexion);

        header('Location: lcategoriastransaccion?m=1');

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_mod
#region try
try {
    $modcateg = CategoriaTransaccion::get($idcategoriatransaccion, $conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/ecategoriatransaccion.view.php';

?>