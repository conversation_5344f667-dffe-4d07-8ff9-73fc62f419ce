<?php

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use PHP<PERSON>ailer\PHPMailer\Exception;

// autoload es el archivo que carga todas las dependencias de la libreria excel phpspreadsheet.
require_once __ROOT__ . '/vendor/autoload.php';

//Create an instance; passing `true` enables exceptions

/**
 * @throws Exception
 */
function sendemail_reminderstodo($reminderstodo): void
{
    $mail = new PHPMailer(true);

    try {
        $correo = "<EMAIL>";

        //Server settings
        //$mail->SMTPDebug = SMTP::DEBUG_SERVER;                      //Enable verbose debug output
        $mail->isSMTP();                                            //Send using SMTP
        $mail->Host = 'smtp.hostinger.com';                    //Set the SMTP server to send through
        $mail->SMTPAuth = true;                                   //Enable SMTP authentication
        $mail->Username = $correo;                     //SMTP username
        $mail->Password = "Corerm.203";                               //SMTP password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;            //Enable implicit TLS encryption
        $mail->Port = 465;                                    //TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`

        //Recipients
        $mail->setFrom($correo, $correo);
        $mail->addAddress("<EMAIL>");
        $mail->addReplyTo($correo, $correo);
        //$mail->addCC('<EMAIL>');
        //$mail->addBCC('<EMAIL>');

        //Attachments
        //$mail->addAttachment('/var/tmp/file.tar.gz');         //Add attachments
        //$mail->addAttachment('/tmp/image.jpg', 'new.jpg');    //Optional name

        //Content
        $mail->isHTML(true);                                  //Set email format to HTML
        $mail->Subject = 'Reminders To-Do';

        // mensaje
        $mensaje = ' Listado de reminders pendientes:<br><br>';

        /** @var ReminderTodo[] $reminderstodo */
        foreach ($reminderstodo as $remindertodo) {
            $mensaje .= '- ' . $remindertodo->description . '.<br>';
        }

        $mensaje .= '<br>';
        $mensaje .= 'Revisar: https://mydash.sicsatech.com/lreminderstodo';

        $mail->Body = $mensaje;
        //$mail->AltBody = 'This is the body in plain text for non-HTML mail clients';

        $mail->send();

    } catch (Exception $e) {
        throw new Exception($e . '<br>' . $mail->ErrorInfo);
    }
}

?>