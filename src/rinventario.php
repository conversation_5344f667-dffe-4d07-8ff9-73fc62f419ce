<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/inventario.php';
require_once __ROOT__ . '/src/classes/inventariocategoria.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['d'])) {
            $success_display = 'show';
            $success_text = 'El inventario ha sido eliminado.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_editinventario
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editinventario'])) {
    try {
        $_SESSION['idinventario'] = limpiar_datos($_POST['sidinventario']);

        header('Location: einventario');

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editinventario
#region try
try {
    $inventario = Inventario::getListAll($conexion);

    $param = array();
    $param['includetrk'] = 1;
    $budgets = Budget::getList($param,$conexion);

    $disponible = Budget::getSumValor($budgets);

    $param = array();
    $param['disponible'] = $disponible;
    $param['dias'] = 7;

    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);

    $param['dias'] = 15;

    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);

    $param['dias'] = 30;

    $disponiblesxdias[] = Budget::calculateDisponiblePorDia($param, $conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try


require_once __ROOT__ . '/views/rinventario.view.php';

?>