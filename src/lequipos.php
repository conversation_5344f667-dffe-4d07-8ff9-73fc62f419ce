<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/equipo.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_addequipo_agregar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addequipo_agregar'])) {
    try {
        $new_equipo         = new Equipo();
        $new_equipo->nombre = limpiar_datos($_POST['addequipo_nombre']);
        $new_equipo->logo   = '';
        $new_equipo->agregar($conexion);

        $success_display = 'show';
        $success_text = 'Equipo agregado.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addequipo_agregar
#region try
try {
    $equipos = Equipo::get_list(array(), $conexion);
    
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lequipos.view.php';

?>

