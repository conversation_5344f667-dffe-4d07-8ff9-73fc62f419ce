<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/deuda.php';
require_once __ROOT__ . '/src/classes/transaccion.php';
require_once __ROOT__ . '/src/classes/categoriatransaccion.php';
require_once __ROOT__ . '/src/classes/transaccioncategoria.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['iddeuda'])) {
            $iddeuda = $_SESSION['iddeuda'];

            // logic:

            unset($_SESSION['iddeuda']);
        } else {
            header('Location: ldeudas');
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $iddeuda = limpiar_datos($_POST['iddeuda']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $conexion->beginTransaction();

        // modify deuda details
        $moddeuda = new Deuda;
        $moddeuda->id = $iddeuda;
        $moddeuda->fechaapagar = limpiar_datos($_POST['fechaapagar']);
        $moddeuda->modifyFechaPagar($conexion);

        // add transaccion
        $selcategs = $_POST['categorias'];

        $newtransaccion = new Transaccion();
        $newtransaccion->valor = limpiar_datos($_POST['valor']);
        $newtransaccion->fecha = limpiar_datos($_POST['fecha']);
        $newtransaccion->nota = limpiar_datos($_POST['nota']);
        $newtransaccion->budget = new Budget();
        $newtransaccion->budget->id_budget = limpiar_datos($_POST['idbudget']);
        $newtransaccion->getListCategorias($selcategs);
        $newtransaccion->add($conexion);

        $conexion->commit();

        header('Location: ldeudas?p=1');
        exit();

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add
#region try
try {
    $moddeuda = Deuda::get($iddeuda, $conexion);
    $categs = CategoriaTransaccion::getList($conexion);

    $param = array();
    $param['includetrk'] = 0;
    $budgets = Budget::getList($param, $conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/pdeuda.view.php';

?>

