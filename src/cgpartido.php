<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_mod
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_mod'])) {
    try {
        $method_sub_mod = 1;
        
        $actconfig                                       = new Config;
        $actconfig->valmin_valor_apuesta                 = limpiar_datos($_POST['valmin_valor_apuesta']);
        $actconfig->betbankroll                          = limpiar_datos($_POST['bankroll']);
        $actconfig->riskkellyformula                     = limpiar_datos($_POST['riskkellyformula']);
        $actconfig->valmin_prediccion                    = limpiar_datos($_POST['valmin_prediccion']);
        $actconfig->riesgo_diario                        = limpiar_datos($_POST['riesgo_diario']);
        $actconfig->fecha_revision_betplay               = limpiar_datos($_POST['fecha_revision_betplay']);
        $actconfig->val_min_avg_all                      = limpiar_datos($_POST['val_min_avg_all']);
        $actconfig->val_min_avg_specific                 = limpiar_datos($_POST['val_min_avg_specific']);
        $actconfig->val_penalidad_mitad_ultimos_partidos = limpiar_datos($_POST['val_penalidad_mitad_ultimos_partidos']);
        $actconfig->val_penalidad_resto_ultimos_partidos = limpiar_datos($_POST['val_penalidad_resto_ultimos_partidos']);
        $actconfig->val_penalidad_same_fixture           = limpiar_datos($_POST['val_penalidad_same_fixture']);
        $actconfig->val_penalidad_1ro_ultimos_partidos   = limpiar_datos($_POST['val_penalidad_1ro_ultimos_partidos']);
        $actconfig->val_penalidad_2do_ultimos_partidos   = limpiar_datos($_POST['val_penalidad_2do_ultimos_partidos']);
        $actconfig->val_penalidad_3ro_ultimos_partidos   = limpiar_datos($_POST['val_penalidad_3ro_ultimos_partidos']);
        $actconfig->min_perc_kelly_crit                  = limpiar_datos($_POST['min_perc_kelly_crit']);
        $actconfig->min_odds                             = limpiar_datos($_POST['min_odds']);
        $actconfig->perc_penalidad_avg_ult_partidos      = limpiar_datos($_POST['perc_penalidad_avg_ult_partidos']);
        $actconfig->modifyConfigApuestas($conexion);

        $success_display = 'show';
        $success_text = 'Modificacion realizada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_mod
#region try
try {
    $method_try = 1;

    $actconfig = Config::get($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/cgpartido.view.php';

?>