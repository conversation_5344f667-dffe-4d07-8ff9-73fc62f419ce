<?php
#region region docs
/** @var Note $sel_note */
/** @var array $notes */
/** @var string $search */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en"  class="dark-mode">
<head>
	<meta charset="utf-8" />
	<title>My Dash | Notes</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
	<meta content="" name="description" />
	<meta content="" name="author" />
	
	<!-- #head -->
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
</head>
<body>
	<!-- BEGIN #loader -->
	<div id="loader" class="app-loader">
		<span class="spinner"></span>
	</div>
	<!-- END #loader -->

	<!-- BEGIN #app -->
    <div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
		<!-- #header -->
		<?php require_once __ROOT__ . '/views/header.view.php'; ?>

		<!-- #sidebar -->
		<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php require_once __ROOT__ . '/views/labels.view.php'; ?>

			<form action="lnotes" method="POST">
				<input type="hidden" id="id_note" name="id_note" value="<?php echo @recover_var($sel_note->id_note) ?>">

				<div class="col" style="display: none">
					<button type="submit" id="sub_select" name="sub_select" class="btn btn-success w-100">
						sub_select
					</button>
				</div>

                <!-- BEGIN row -->
                <div class="row">
	                <!-- BEGIN text -->
	                <div class="col-md-12 col-xs-12">
		                <div class="mb-3">
			                <label class="form-label">Buscar:</label>
			                <input type="text" name="search" id="search" value="<?php echo @recover_var($search) ?>" class="form-control" onclick="this.focus();this.select('')" autofocus/>
		                </div>
	                </div>
	                <!-- END text -->
                </div>
                <!-- END row -->
                <!-- BEGIN row -->
                <div class="row">
                    <div class="col">
                        <button type="submit" id="sub_search" name="sub_search" class="btn btn-success w-100">
                            Buscar
                        </button>
                    </div>
                    <div class="col">
                        <a href="inotes" class="btn btn-primary w-100">
                            Crear
                        </a>
                    </div>
                </div>
                <!-- END row -->
                <!-- BEGIN panel -->
                <div class="panel panel-inverse mt-3">
                    <div class="panel-heading">
                        <h4 class="panel-title">Notes</h4>
                    </div>
                    <!-- BEGIN panel-body -->
                    <div class="panel-body">
                        <table class="table table-hover table-sm">
                            <tbody class="fs-14px">
                            <?php #region region notes ?>
                            <?php foreach ($notes as $note): ?>
                                <tr>
                                    <td class="w-10px">
                                        <button type="button" class="btn btn-info btn-xs" onclick="selectnote(<?php echo limpiar_datos($note->id_note); ?>);">
                                            <i class="fa fa-book fa-lg fa-fw"></i>
                                        </button>
                                    </td>
                                    <td><?php echo limpiar_datos($note->title); ?></td>
                                </tr>
                            <?php endforeach; ?>
                            <?php #endregion ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- END panel-body -->
                </div>
                <!-- END panel -->
                <!-- BEGIN panel -->
                <div class="panel panel-inverse">
                    <div class="panel-heading">
                        <h4 class="panel-title fs-15px">
                            <?php echo limpiar_datos($sel_note->title); ?>
                        </h4>
                        <?php if(isset($id_note)): ?>
                            <div class="btn-group my-n1 me-2">
                                <?php #region region sub_edit ?>
                                <div class="col">
                                    <button type="submit" id="sub_edit" name="sub_edit" class="btn btn-md btn-primary">
                                        Editar
                                    </button>
                                </div>
                                <?php #endregion sub_edit ?>
                            </div>
                            <div class="btn-group my-n1">
                                <div class="col">
                                    <a href="#mdl_delnote" data-bs-toggle="modal" data-idnote="<?php echo limpiar_datos($sel_note->id_note) ?>" class="btn btn-md btn-danger">
                                        Eliminar
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <!-- BEGIN panel-body -->
                    <div class="panel-body fs-15px">
                        <!-- BEGIN row -->
                        <div class="row mt-3">
                            <div class="col-md-6 col-xs-12">
                                <div class="form-floating">
                                    <input type="text" class="form-control fs-15px" name="title" id="title" value="<?php echo @recover_var($sel_note->title) ?>" placeholder="Title:"/>
                                    <label for="title" class="d-flex align-items-center fs-15px">
                                        Title:
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 col-xs-12">
                                <div class="form-floating">
                                    <input type="text" class="form-control fs-15px" name="tags" id="tags" value="<?php echo @recover_var($sel_note->tags) ?>" placeholder="Tags:"/>
                                    <label for="tags" class="d-flex align-items-center fs-15px">
                                        Tags:
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!-- END row -->
                    </div>
                    <!-- END panel-body -->
                    <?php #region region region textarea note ?>
                    <textarea name="detail" class="form-control no-rounded-pill border-4 border-start-0 border-end-0 border-bottom-0 rounded-0 fs-15px" rows="50"><?php echo limpiar_datos($sel_note->detail); ?></textarea>
                    <?php #endregion textarea note ?>
                </div>
                <!-- END panel -->

				<!-- BEGIN #modal-dialog -->
				<div class="modal fade" id="mdl_delnote">
					<div class="modal-dialog modal-dialog-centered">
						<div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title">Delete note</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                            </div>
                            <div class="modal-body">
                                <p>Are you sure you want to delete this note?</p>
                            </div>
                            <div class="modal-footer">
                                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
                                    <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                                </a>
                                <button type="submit" id="sub_del" name="sub_del" class="btn btn-danger">
                                    <i class="fa fa-check fa-lg fa-fw"></i>
                                </button>
                            </div>
						</div>
					</div>
				</div>
				<!-- END #modal-dialog -->		

                <!-- BEGIN sub_edit -->
                <button type="submit" id="sub_edit2" name="sub_edit" class="region_SUBMIT_sub_edit btn btn-icon btn-circle btn-success floating-button">
                    <i class="fa fa-floppy-disk"></i>
                </button>
                <!-- END sub_edit -->
			</form>			
		</div>
		<!-- END #content -->
		
		<!-- BEGIN scroll-top-btn -->
		<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
	</div>
	<!-- END #app -->

	<!-- ================== BEGIN core-js ================== -->
	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
	<!-- ================== END core-js ================== -->

	<script type="text/javascript">
		function selectnote(selectedid_note) {
			var id_note = document.getElementById('id_note');
			id_note.value = selectedid_note;

			document.getElementById('sub_select').click();
		}

		// Get the input field
		var input = document.getElementById("search");

		input.addEventListener("keypress", function(event) {
			if (event.key === "Enter") {
				// Cancel the default action, if needed
				event.preventDefault();
				// Trigger the button element with a click
				document.getElementById("sub_search").click();
			}
		});
	</script>
</body>
</html>