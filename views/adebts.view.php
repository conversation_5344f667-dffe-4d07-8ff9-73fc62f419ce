<!DOCTYPE html>
<html lang="en"  class="dark-mode">
<head>
	<title>My Dash | Debts</title>
	<meta content="" name="description" />
	<meta content="" name="author" />

    <!-- #head -->
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>

    <!-- ================== BEGIN page-css ================== -->
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet" />
    <!-- ================== END page-css ================== -->
</head>
<body>
    <!-- #loader -->
    <?php require_once __ROOT__ . '/views/loader.view.php'; ?>

    <!-- BEGIN #app -->
    <div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
        <!-- #header -->
        <?php require_once __ROOT__ . '/views/header.view.php'; ?>

        <!-- #sidebar -->
        <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

        <!-- BEGIN #content -->
		<div id="content" class="app-content">
            <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

			<!-- BEGIN breadcrumb -->
			<ol class="breadcrumb float-xl-end">
				<li class="breadcrumb-item"><a href="<?php echo htmlspecialchars(RUTA) ?>dashboard">Dashboard</a></li>
				<li class="breadcrumb-item active"><a href="javascript:;">Debts</a></li>
			</ol>
			<!-- END breadcrumb -->

			<!-- BEGIN page-header -->
			<h1 class="page-header">Debts</h1>
			<!-- END page-header -->

            <!-- BEGIN panel -->
			<div class="panel panel-inverse spacing-08em">
				<div class="panel-heading">
					<h4 class="panel-title">Debts list</h4>
				</div>
				<div class="panel-body">
                    <div class="row">
                        <div class="col">
                            <a href="#mdl_add" data-bs-toggle="modal" class="btn btn-lg btn-primary w-100">
								<i class="fa fa-arrow-down fa-lg fa-fw"></i>
							</a>
                        </div>
                        <div class="col">
                            <a href="#mdl_reset" data-bs-toggle="modal" class="btn btn-lg btn-danger w-100">
								<i class="fa fa-arrows-rotate fa-lg fa-fw"></i>
							</a>
                        </div>                        
                    </div>
                    
					<div class="fs-16px">
						<div class="row mt-3 mb-1">
							<div class="col-2">
								<span class="semi-bold text-gray-100">Collect within 8 days:</span>
							</div>
							<div class="col-2 text-end text-warning semi-bold">
								$ <?php echo format_currency_usd(limpiar_datos($sum['sumcop'])) ?> COP
							</div>
							<div class="col-2 text-end text-warning semi-bold">
								$ <?php echo format_currency_usd(limpiar_datos($sum['sumusd'])) ?> USD
							</div>
						</div>

						<div class="row mt-3 mb-1">
							<div class="col-2">
								<span class="semi-bold text-gray-100">Delayed:</span>
							</div>
							<div class="col-2 text-end text-danger semi-bold">
								$ <?php echo format_currency_usd(limpiar_datos($sumdelayed['sumcop'])) ?> COP
							</div>
							<div class="col-2 text-end text-danger semi-bold">
								$ <?php echo format_currency_usd(limpiar_datos($sumdelayed['sumusd'])) ?> USD
							</div>
						</div>
					</div>

                    <table class="table table-hover table-sm mt-3">
                        <thead>
                            <tr>
                                <th class="w-40px"></th>
                                <th class="w-40px"></th>
                                <th class="w-60px text-start">USD</th>
                                <th class="text-center">Title</th>
                                <th class="text-center">Value</th>
                                <th class="text-center"># Debtors</th>
                                <th class="text-center">COP</th>
                                <th class="text-center">USD</th>
                                <th class="text-center">Date</th>
                                <th class="text-center">Days left</th>
                                <th class="text-center">Info</th>
								<th></th>
                            </tr>
                        </thead>
                        <tbody class="fs-14px">
                            <?php foreach ($debts_paid as $debt): ?>
                                <tr class="text-success">
                                    <td></td>
                                    <td>
										<a class="btn btn-primary btn-xs" 
										data-bs-toggle="modal" 
										data-bs-target="#mdl_mod"
										data-iddebt="<?php echo limpiar_datos($debt->ID) ?>"
										data-title="<?php echo limpiar_datos($debt->title) ?>"
										data-value="<?php echo limpiar_datos($debt->value) ?>"
										data-datedebt="<?php echo limpiar_datos($debt->date) ?>"
										>
											<i class="fa fa-pencil-alt fa-lg fa-fw"></i>
										</a>	     
									</td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" <?php if($debt->isusd == 1) echo "checked" ?> disabled />
                                        </div>
                                    </td>
                                    <!-- click to modify debt #2 -->
                                    <td>
                                        <span role="button"><?php echo limpiar_datos($debt->title); ?></span>
                                    </td>                                    
                                    <td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debt->value)); ?></td>                                    
                                    <td class="text-center"><?php echo limpiar_datos($debt->ndebtors); ?></td>                                    
                                    <td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debt->valuecop)); ?></td>                                    
                                    <td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debt->valueusd)); ?></td>                                    
                                    <td class="text-center"><?php echo format_date(limpiar_datos($debt->date)); ?></td>                                    
                                    <td class="text-center">--</td>                                    
                                    <td class="text-center">
										<span id="info_<?php echo limpiar_datos($debt->ID) ?>">
											<?php echo limpiar_datos($debt->info); ?>
										</span>
									</td>    
									<td>
										<button 
										type="button" 
										class="btn btn-white btn-xs" 
										data-toggle="clipboard" 
										data-clipboard-target="#info_<?php echo limpiar_datos($debt->ID) ?>"
										>
											<i class="fa fa-clipboard fa-lg fa-fw"></i>
										</button>
									</td>                                
                                </tr>
                            <?php endforeach; ?>

                            <?php foreach ($debts as $debt): ?>
                                <tr class="<?php echo limpiar_datos($debt->rowcolor . ' ' . $debt->fontcolor) ?>">
                                    <td class="w-10px">
										<a class="btn btn-success btn-xs"
										data-bs-toggle="modal" 
										data-bs-target="#mdl_paid" 
										data-iddebt="<?php echo limpiar_datos($debt->ID) ?>"
										data-title="<?php echo limpiar_datos($debt->title) ?>"
										>
											<i class="fa fa-dollar-sign fa-lg fa-fw"></i>
										</a>	                                       
                                    </td>
                                    <td>
										<a class="btn btn-primary btn-xs" 
											 data-bs-toggle="modal" 
											 data-bs-target="#mdl_mod"
											 data-iddebt="<?php echo limpiar_datos($debt->ID) ?>"
											 data-title="<?php echo limpiar_datos($debt->title) ?>"
											 data-value="<?php echo limpiar_datos($debt->value) ?>"
											 data-info="<?php echo limpiar_datos($debt->info) ?>"
											 data-datedebt="<?php echo limpiar_datos($debt->date) ?>"
                                             >
												<i class="fa fa-pencil-alt fa-lg fa-fw"></i>
										</a>	     
									</td>
                                    <td class="w-10px">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" <?php if($debt->isusd == 1) echo "checked" ?> disabled />
                                        </div>
                                    </td>
                                    <!-- click to modify debt #2 -->
                                    <td><?php echo limpiar_datos($debt->title); ?></td>                                    
                                    <td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debt->value)); ?></td>                                    
                                    <td class="text-center"><?php echo limpiar_datos($debt->ndebtors); ?></td>                                    
                                    <td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debt->valuecop)); ?></td>                                    
                                    <td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debt->valueusd)); ?></td>                                    
                                    <td class="text-center"><?php echo format_date(limpiar_datos($debt->date)); ?></td>                                    
                                    <td class="text-center"><?php echo limpiar_datos($debt->daysleft); ?></td>                                    
                                    <td class="text-center">                                        
										<span id="info_<?php echo limpiar_datos($debt->ID) ?>">
											<?php echo limpiar_datos($debt->info); ?>
										</span>
                                    </td>     
									<td>
										<button 
										type="button" 
										class="btn btn-white btn-xs" 
										data-toggle="clipboard" 
										data-clipboard-target="#info_<?php echo limpiar_datos($debt->ID) ?>"
										>
											<i class="fa fa-clipboard fa-lg fa-fw"></i>
										</button>
									</td>                                  
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
				</div>
			</div>
			<!-- END panel -->

			<!-- BEGIN panel -->
			<div class="panel panel-inverse mt-3">
				<div class="panel-heading">
					<h4 class="panel-title">Next cycle</h4>
				</div>
				<!-- BEGIN panel-body -->
				<div class="panel-body">
					<table class="table table-hover table-sm">
						<thead>
							<tr>
								<th class="w-80px"></th>
                                <th class="w-60px text-start">USD</th>
                                <th class="text-center">Title</th>
                                <th class="text-center">Value</th>
                                <th class="text-center"># Debtors</th>
                                <th class="text-center">COP</th>
                                <th class="text-center">USD</th>
                                <th class="text-center">Date</th>
                                <th class="text-center">Days left</th>
                                <th class="text-center">Info</th>
							</tr>
						</thead>
						<tbody class="fs-14px">
							<?php foreach ($debtsnext as $debtnext): ?>
								<tr>
									<td></td>
									<td class="w-10px">
										<div class="form-check form-switch">
											<input class="form-check-input" type="checkbox" <?php if($debt->isusd == 1) echo "checked" ?> disabled />
										</div>
									</td>
									<td>
										<?php echo limpiar_datos($debtnext->title); ?></td>                                    
										<td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debtnext->value)); ?></td>                                    
										<td class="text-center"><?php echo limpiar_datos($debtnext->ndebtors); ?></td>                                    
										<td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debtnext->valuecop)); ?></td>                                    
										<td class="text-end">$ <?php echo format_currency_usd(limpiar_datos($debtnext->valueusd)); ?></td>                                    
										<td class="text-center"><?php echo format_date(limpiar_datos($debtnext->date)); ?></td>                                    
										<td class="text-center"><?php echo limpiar_datos($debtnext->daysleft); ?></td>                                    
										<td class="text-center">
										<label class="user-select-all"><?php echo limpiar_datos($debtnext->info); ?></label>
									</td>   
								</tr>		
							<?php endforeach; ?>
						</tbody>
					</table> 
				</div>
				<!-- END panel-body -->
			</div>
			<!-- END panel -->



            <!-- BEGIN #modal-dialog -->
            <div class="modal fade" id="mdl_paid">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <form action="debts" method="POST">
                            <div class="modal-header">
                                <input type="hidden" id="paid_iddebt" name="paid_iddebt">

                                <h4 class="modal-title">Mark as paid</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                            </div>
                            <div class="modal-body">
                                <!-- BEGIN row -->
                                <div class="row">
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px datepicker" id="paid_date" name="paid_date" />
                                            <label for="paid_date" class="d-flex align-items-center fs-15px">
                                                New date:
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <!-- END row -->
                            </div>
                            <div class="modal-footer">
                                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
                                <button type="submit" id="sub_paid" name="sub_paid" class="btn btn-success">
									<i class="fa fa-check fa-lg fa-fw"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END #modal-dialog -->

            <!-- BEGIN #modal-dialog -->
            <div class="modal fade" id="mdl_add">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <form action="debts" method="POST">
                            <div class="modal-header">
                                <h4 class="modal-title">Add debt</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px" name="add_title" id="add_title"/>
                                            <label for="add_title" class="d-flex align-items-center fs-15px">
                                                Title
                                            </label>
                                        </div>
                                    </div>        
                                </div>
                                <div class="row mb-3">
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px" name="add_ndebtors" id="add_ndebtors"/>
                                            <label for="add_ndebtors" class="d-flex align-items-center fs-15px">
                                            # Debtors
                                            </label>
                                        </div>
                                    </div>        
                                </div>
                                <div class="row mb-3">
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px" name="add_value" id="add_value"/>
                                            <label for="add_value" class="d-flex align-items-center fs-15px">
                                            Value
                                            </label>
                                        </div>
                                    </div>        
                                </div>
                                <div class="row mb-3">                           
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px datepicker" id="add_date" name="add_date" />
                                            <label for="add_date" class="d-flex align-items-center fs-15px">
                                                Date
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px" name="add_info" id="add_info"/>
                                            <label for="add_info" class="d-flex align-items-center fs-15px">
                                            Info
                                            </label>
                                        </div>
                                    </div>        
                                </div>
                                <div class="row">
                                    <div class="col">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="add_isusd" name="add_isusd" />
                                            <label class="form-check-label" for="add_isusd">Is USD</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
                                <button type="submit" id="sub_add" name="sub_add" class="btn btn-success">
									<i class="fa fa-check fa-lg fa-fw"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END #modal-dialog -->

            <!-- BEGIN #modal-dialog -->
            <div class="modal fade" id="mdl_mod">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <form action="debts" method="POST">
                            <div class="modal-header">
                                <input type="hidden" id="mod_iddebt" name="mod_iddebt">

                                <h4 class="modal-title">Modify debt</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px" name="mod_title" id="mod_title"/>
                                            <label for="mod_title" class="d-flex align-items-center fs-15px">
                                                Title
                                            </label>
                                        </div>
                                    </div>        
                                </div>
                                <div class="row mb-3">
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px" name="mod_value" id="mod_value"/>
                                            <label for="mod_value" class="d-flex align-items-center fs-15px">
                                                Value
                                            </label>
                                        </div>
                                    </div>        
                                </div>
                                <div class="row mb-3">
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px" name="mod_info" id="mod_info"/>
                                            <label for="mod_info" class="d-flex align-items-center fs-15px">
                                                Info
                                            </label>
                                        </div>
                                    </div>        
                                </div>
                                <div class="row mb-3">                           
                                    <div class="col">
                                        <div class="form-floating">
                                            <input type="text" class="form-control fs-15px datepicker" id="mod_date" name="mod_date" />
                                            <label for="mod_date" class="d-flex align-items-center fs-15px">
                                                Date
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
                                <button type="submit" id="sub_del" name="sub_del" class="btn btn-danger">
									<i class="fa fa-times fa-lg fa-fw"></i>
                                </button>
                                <button type="submit" id="sub_mod" name="sub_mod" class="btn btn-success">
									<i class="fa fa-check fa-lg fa-fw"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END #modal-dialog -->

            <!-- BEGIN #modal-dialog -->
            <div class="modal fade" id="mdl_reset">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <form action="debts" method="POST">
                            <div class="modal-header">
                                <h4 class="modal-title">Reset debts</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                            </div>
                            <div class="modal-body">
                                <p class="fs-15px">¿Are you sure you want to reset the debts?</p>
                            </div>
                            <div class="modal-footer">
                                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
                                <button type="submit" id="sub_reset" name="sub_reset" class="btn btn-success">
									<i class="fa fa-check fa-lg fa-fw"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END #modal-dialog -->

		</div>
		<!-- END #content -->

        <!-- #scrolltop -->
        <?php require_once __ROOT__ . '/views/scrolltop.view.php'; ?>
    </div>
    <!-- END #app -->

    <!-- #js -->
    <?php require_once __ROOT__ . '/views/js.view.php'; ?>
    <?php require_once __ROOT__ . '/views/datepickerjs.view.php'; ?>

	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/clipboard/dist/clipboard.min.js"></script>

    <script type="text/javascript">
        $('#mdl_paid').on('shown.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var recipient_iddebt = button.data('iddebt');
            var recipient_title = button.data('title');

            var iddebt = document.getElementById('paid_iddebt');
            var title = document.getElementById('paid_title');

            iddebt.value = recipient_iddebt;
            title.innerHTML = recipient_title;
        })
                                
        $('#mdl_mod').on('shown.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var recipient_iddebt = button.data('iddebt');
            var recipient_title = button.data('title');
            var recipient_value = button.data('value');
            var recipient_info = button.data('info');
            var recipient_datedebt = button.data('datedebt');

            var iddebt = document.getElementById('mod_iddebt');
            var title = document.getElementById('mod_title');
            var value = document.getElementById('mod_value');
            var info = document.getElementById('mod_info');
            var datedebt = document.getElementById('mod_date');

            iddebt.value = recipient_iddebt;
            title.value = recipient_title;
            value.value = recipient_value;
            info.value = recipient_info;
            datedebt.value = recipient_datedebt;
        })

		var clipboard = new ClipboardJS("[data-toggle='clipboard']");
  
		clipboard.on("success", function(e) {
			$(e.trigger).tooltip({
			title: "Copied",
			placement: "top"
			});
			$(e.trigger).tooltip("show");
			setTimeout(function() {
			$(e.trigger).tooltip("dispose");
			}, 500);
		});
    </script>
</body>
</html>