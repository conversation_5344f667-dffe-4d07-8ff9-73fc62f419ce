<?php #region region JS AJAX sub_crear_sector ?>
<script type="text/javascript">
    $(document).ready(function() {
        $('#sub_crear_sector').on('click', function(event) {
            event.preventDefault();

            var crear_sector_nombre = $('#crear_sector_nombre').val();

            $.ajax({
                url: 'istock', 
                method: 'POST',
                dataType: 'json',
                data: { 
                    action: 'button_crear_sector_clicked',
                    crear_sector_nombre: crear_sector_nombre,
                    ajax_check: 1,
                },
                success: function(response) {
                    $('#mdl_crear_sector').modal('hide');              
                    $('#sector').val(response.sector);   

                    setTimeout(function() {
                        $('#sector').trigger('focus');  
                        $('#sector').trigger('select');  
                    }, 1700); 
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error("AJAX Error: " + textStatus);
                    console.error("Error Thrown: " + errorThrown);
                    console.error("Response Text: " + jqXHR.responseText);
                    alert('An error occurred: ' + textStatus);
                }
            });
        });
    });
</script>
<?php #endregion JS AJAX sub_crear_sector ?>
<?php #region region JS AJAX sub_crear_industria ?>
<script type="text/javascript">
    $(document).ready(function() {
        $('#sub_crear_industria').on('click', function(event) {
            event.preventDefault();
            
            var crear_industria_nombre = $('#crear_industria_nombre').val();

            $.ajax({
                url: 'istock', 
                method: 'POST',
                dataType: 'json',
                data: { 
                    action: 'button_crear_industria_clicked',
                    crear_industria_nombre: crear_industria_nombre,
                    ajax_check: 1,
                },
                success: function(response) {
                    $('#mdl_crear_industria').modal('hide');              
                    $('#industria').val(response.industria);   

                    setTimeout(function() {
                        $('#industria').trigger('focus');  
                        $('#industria').trigger('select');  
                    }, 1700);  
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error("AJAX Error: " + textStatus);
                    console.error("Error Thrown: " + errorThrown);
                    console.error("Response Text: " + jqXHR.responseText);
                    alert('An error occurred: ' + textStatus);
                }
            });
        });
    });
</script>
<?php #endregion JS AJAX sub_crear_industria ?>