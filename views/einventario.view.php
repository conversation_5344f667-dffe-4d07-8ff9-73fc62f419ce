<?php
#region region docs
/** @var Inventario $modinventario */
/** @var InventarioCategoria[] $categorias */
/** @var string $idinventario */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Inventario</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region head ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo RUTA ?>resources/assets/plugins/abpetkov-powerange/dist/powerange.min.css" rel="stylesheet"/>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">Editar inventario</h1>
        <!-- END page-header -->

        <?php #region region form ?>
        <form action="einventario" method="POST">
            <input type="hidden" id="idinventario" name="idinventario" value="<?php echo @recover_var($idinventario) ?>">

            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN select -->
                <div class="col-md-5 col-xs-12">
                    <div class="form-floating">
                        <select id="idinventariocategoria" name="idinventariocategoria" class="form-select">
                            <option value="">--</option>

                            <?php foreach ($categorias as $categoria): ?>
                                <option <?php @recover_var_list(ordena($modinventario->inventariocategoria->id), ordena($categoria->id)) ?> value="<?php echo limpiar_datos($categoria->id); ?>">
                                    <?php echo limpiar_datos($categoria->nombre); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <label for="idinventariocategoria" class="d-flex align-items-center fs-15px">
                            Categoria:
                        </label>
                    </div>
                </div>
                <!-- END select -->
                <!-- BEGIN text -->
                <div class="col-md-7 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px text-uppercase" name="nombre" id="nombre" value="<?php echo @recover_var($modinventario->nombre) ?>" placeholder="Nombre:"/>
                        <label for="nombre" class="d-flex align-items-center fs-15px">
                            Nombre:
                        </label>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-4">
                <!-- BEGIN slider -->
                <div class="col-md-12 col-xs-12">
                    <label for="estadoqty" class="fs-15px">
                        Estado qty (<span id="estadoqtyvalue"></span>):
                    </label>
                    <div class="powerange-wrapper">
                        <input name="estadoqty" id="estadoqty" type="text" value="<?php echo @recover_var($modinventario->estadoqty) ?>" class="powerange-default"/>
                    </div>
                </div>
                <!-- END slider -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <div class="col-md-3 col-xs-12">
                    <a href="rinventario" class="btn btn-default w-100">
                        Regresar
                    </a>
                </div>
                <div class="col">
                    <a class="btn btn-danger w-100" href="#mdl_dinventario" data-bs-toggle="modal">
                        Eliminar
                    </a>
                </div>
                <?php #region region sub_mod ?>
                <div class="col-md-5 col-xs-12">
                    <button type="submit" id="sub_mod" name="sub_mod" class="btn btn-success w-100">
                        Modificar
                    </button>
                </div>
                <?php #endregion sub_mod ?>
            </div>
            <!-- END row -->
            <?php #region region mdl_dinventario ?>
            <div class="modal fade" id="mdl_dinventario">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Eliminar inventario</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <p>Esta seguro que desea eliminar este inventario?</p>
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                                <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                            </a>
                            <button type="submit" id="sub_delinventario" name="sub_delinventario" class="btn btn-danger">
                                <i class="fa fa-check fa-lg fa-fw"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion mdl_dinventario ?>
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region js ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region js slider ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/abpetkov-powerange/dist/powerange.min.js"></script>
<!-- script -->
<script>
    var elm = document.getElementById('estadoqty');
    var powerange = new Powerange(elm, {
        disable: false,
        disableOpacity: 0.5,
        decimal: false,
        hideRange: false,
        klass: '',
        min: 0,
        max: 2,
        start: <?php echo @recover_var($modinventario->estadoqty) ?>,
        step: null,
        vertical: false,
        callback: displayValue
    });

    elm.value;  // get value

    function displayValue() {
        document.getElementById('estadoqtyvalue').innerHTML = elm.value;
    }
</script>
<?php #endregion js slider ?>
<?php #endregion js ?>

</body>
</html>