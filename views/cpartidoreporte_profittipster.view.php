<?php
#region region DOCS

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Reportes</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h4>Profit por tipster</h4>

        <hr>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="cpartidoreporte_profittipster" method="POST">
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN select -->
                <div class="col-md-12 col-xs-12">
                    <label for="num_semana" class="d-flex align-items-center fs-12px">
                        Num. semana:
                    </label>
                    <select id="num_semana" name="num_semana" class="form-select form-control-fh no-border-radious">
                        <option value="">--</option>
            
                        <?php foreach ($semanas as $semana): ?>
                            <option <?php @recover_var_list($numsemana, $semana->num_week) ?> value="<?php echo limpiar_datos($semana->num_week); ?>">
                                <?php echo limpiar_datos($semana->num_week); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <!-- END select -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- sub_search -->
                <div class="col-md-8 col-xs-12">
                    <button type="submit" id="sub_search" name="sub_search" class="region_SUBMIT_sub_search btn btn-xs btn-primary w-100 no-border-radious">
                        Buscar
                    </button>
                </div>
                <!-- END sub_search -->
                <!-- BEGIN link -->
                <div class="col-md-4 col-xs-12">
                    <a href="lpartidosreportes" class="region_LINK_regresar btn btn-xs btn-default w-100 no-border-radious">
                        Regresar
                    </a>
                </div>
                <!-- END link -->
            </div>
            <!-- END ROW -->
            <!-- PANEL -->
            <div class="region_PANEL_reporte panel panel-inverse mt-3 no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">
                        Reporte:
                    </h4>
                </div>
                <!-- BEGIN PANEL body -->
                <div class="table-nowrap" style="overflow: auto">
                    <!-- TABLE -->
                    <table class="region_TABLE_reporte table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Tipster</th>
                            <th class="text-center"># apuestas</th>
                            <th class="text-center">Win ratio</th>
                            <th class="text-center">Apostado</th>
                            <th class="text-center">Profit $</th>
                            <th class="text-center">Profit %</th>
                        </tr>
                        </thead>
                        <tbody class="region_ARRAY_reporte fs-12px">
                            <?php for ($i = 0; $i < count($reporte); $i++): ?>
                            <?php if($reporte[$i]['calculos']['num_apuestas'] > 0) : ?>
                                <tr class="<?php echo ($reporte[$i]['calculos']['porc_profit'] > 0) ? COLOR_SUCCESS : COLOR_DANGER; ?>">
                                    <td><?php echo $reporte[$i]['tipster']; ?></td>
                                    <td class="text-center"><?php echo $reporte[$i]['calculos']['num_apuestas']; ?></td>
                                    <td class="text-end"><?php echo $reporte[$i]['calculos']['winratio']; ?>%</td>
                                    <td class="text-end"><?php echo formatCurrencyConSigno($reporte[$i]['calculos']['val_apostado']); ?></td>
                                    <td class="text-end"><?php echo formatCurrencyConSigno($reporte[$i]['calculos']['val_profit']); ?></td>
                                    <td class="text-end"><?php echo $reporte[$i]['calculos']['porc_profit']; ?>%</td>
                                </tr>
                            <?php endif; ?>
                            <?php endfor; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->
                </div>
                <!-- END PANEL body -->
            </div>
            <!-- END PANEL -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script id="region_JS" src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #endregion JS ?>

</body>
</html>