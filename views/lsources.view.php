<?php
#region region DOCS
/** @var Source[] $sources */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Sources</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <?php #region region CSS datatable ?>
    <link href="<?php echo RUTA ?>resources/assets/plugins/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
    <link href="<?php echo RUTA ?>resources/assets/plugins/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" rel="stylesheet" />
    <?php #endregion CSS datatable ?>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">

        <!-- BEGIN page-header -->
        <h4>Sources</h4>
        
        <hr>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="lsources" method="POST">
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN LINK -->
                <div class="col-md-12 col-xs-12">
                    <a href="#mdl_addsource" data-bs-toggle="modal" class="btn btn-xs btn-primary w-100 no-border-radious">
                        Agregar
                    </a>
                </div>
                <!-- END LINK -->
            </div>
            <!-- END ROW -->
            <?php #region region PANEL sources ?>
            <div class="panel panel-inverse mt-3 no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">
                        Sources:
                    </h4>
                </div>
                <!-- BEGIN PANEL body -->
                <div class="p-1 table-nowrap" style="overflow: auto">
                    <?php #region region TABLE sources ?>
                    <table class="table table-hover table-sm data-table-default">
                        <thead>
                        <tr>
                            <th class="text-center">Nombre</th>                            
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php foreach ($sources as $source): ?>
                            <tr class="cursor-pointer">
                                <td><?php echo $source->nombre; ?></td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE sources ?>
                </div>
                <!-- END PANEL body -->
            </div>
            <?php #endregion PANEL sources ?>
            <?php #region region MODAL mdl_addsource ?>
            <div class="modal fade" id="mdl_addsource">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Agregar source</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <!-- BEGIN text -->
                            <div class="col-md-12 col-xs-12">
                                <div class="input-group">
                                    <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                        Nombre:
                                    </span>
                                    <input type="text" name="addsource_nombre" id="addsource_nombre" class="form-control form-control-fh fs-12px no-border-radious text-uppercase"/>
                                </div>
                            </div>
                            <!-- END text -->
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-xs btn-default no-border-radious" data-bs-dismiss="modal">
                                Cancelar
                            </a>
                            <button type="submit" id="sub_addsource_agregar" name="sub_addsource_agregar" class="btn btn-xs btn-success no-border-radious">
                                Agregar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion MODAL mdl_addsource ?>
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
<?php #region region JS modal text focus when shown ?>
<script type="text/javascript">
    $('#mdl_addsource').on('shown.bs.modal', function () {
        $('#addsource_nombre').trigger('focus');
    });
</script>
<?php #region JS modal text focus when shown ?>
<?php #region region JS pressenter ?>
<script>
    pressenterandclick('addsource_nombre','sub_addsource_agregar');
</script>
<?php #endregion JS pressenter ?>
<?php #region region JS datatable ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"></script>
<script>
    $('.data-table-default').DataTable({
        responsive: true,
        language: {
            "search": "Buscar:",
        },
        info: false,
        paging: false,
        ordering: true
        //order: [[1, 'asc']] //option for ordering certain column by default, column number starts from 0, 'asc' for ascendent and 'desc' for descendant
    });
</script>
<?php #endregion JS datatable ?>

<?php #endregion JS ?>

</body>
</html>