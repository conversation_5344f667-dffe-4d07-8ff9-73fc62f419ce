{"version": 3, "file": "main.js", "sources": ["src/ListViewHeaderRow.tsx", "src/ListViewEventRow.tsx", "src/ListView.tsx", "src/options.ts", "src/main.ts"], "sourcesContent": ["import {\n  BaseComponent, DateMarker, createElement, DateRang<PERSON>, getDate<PERSON>eta,\n  RenderHook, DayHeaderContentArg, getDayClassNames, formatDayString, Fragment, buildNavLinkAttrs, getUniqueDomId,\n} from '@fullcalendar/common'\n\nexport interface ListViewHeaderRowProps {\n  cellId: string\n  dayDate: DateMarker\n  todayRange: DateRange\n}\n\ninterface HookProps extends DayHeaderContentArg { // doesn't enforce much since DayCellContentArg allow extra props\n  textId: string // for aria-labelledby\n  text: string\n  sideText: string\n}\n\nexport class ListViewHeaderRow extends BaseComponent<ListViewHeaderRowProps> {\n  state = {\n    textId: getUniqueDomId(),\n  }\n\n  render() {\n    let { theme, dateEnv, options, viewApi } = this.context\n    let { cellId, dayDate, todayRange } = this.props\n    let { textId } = this.state\n    let dayMeta = getDateMeta(dayDate, todayRange)\n\n    // will ever be falsy?\n    let text = options.listDayFormat ? dateEnv.format(dayDate, options.listDayFormat) : ''\n\n    // will ever be falsy? also, BAD NAME \"alt\"\n    let sideText = options.listDaySideFormat ? dateEnv.format(dayDate, options.listDaySideFormat) : ''\n\n    let hookProps: HookProps = {\n      date: dateEnv.toDate(dayDate),\n      view: viewApi,\n      textId,\n      text,\n      sideText,\n      navLinkAttrs: buildNavLinkAttrs(this.context, dayDate),\n      sideNavLinkAttrs: buildNavLinkAttrs(this.context, dayDate, 'day', false),\n      ...dayMeta,\n    }\n\n    let classNames = ['fc-list-day'].concat(\n      getDayClassNames(dayMeta, theme),\n    )\n\n    // TODO: make a reusable HOC for dayHeader (used in daygrid/timegrid too)\n    return (\n      <RenderHook<HookProps>\n        hookProps={hookProps}\n        classNames={options.dayHeaderClassNames}\n        content={options.dayHeaderContent}\n        defaultContent={renderInnerContent}\n        didMount={options.dayHeaderDidMount}\n        willUnmount={options.dayHeaderWillUnmount}\n      >\n        {(rootElRef, customClassNames, innerElRef, innerContent) => (\n          <tr\n            ref={rootElRef}\n            className={classNames.concat(customClassNames).join(' ')}\n            data-date={formatDayString(dayDate)}\n          >\n            {/* TODO: force-hide top border based on :first-child */}\n            <th scope=\"colgroup\" colSpan={3} id={cellId} aria-labelledby={textId}>\n              <div className={'fc-list-day-cushion ' + theme.getClass('tableCellShaded')} ref={innerElRef}>\n                {innerContent}\n              </div>\n            </th>\n          </tr>\n        )}\n      </RenderHook>\n    )\n  }\n}\n\nfunction renderInnerContent(props: HookProps) {\n  return (\n    <Fragment>\n      {props.text && (\n        <a id={props.textId} className=\"fc-list-day-text\" {...props.navLinkAttrs}>\n          {props.text}\n        </a>\n      )}\n      {props.sideText && (/* not keyboard tabbable */\n        <a aria-hidden className=\"fc-list-day-side-text\" {...props.sideNavLinkAttrs}>\n          {props.sideText}\n        </a>\n      )}\n    </Fragment>\n  )\n}\n", "import {\n  MinimalEventProps, BaseComponent, ViewContext, createElement, AllDayContentArg,\n  Seg, isMultiDay<PERSON>ange, DateFormatter, buildSegTimeText, createFormatter, EventRoot, ComponentChildren, RenderHook, getSegAnchorAttrs,\n} from '@fullcalendar/common'\n\nconst DEFAULT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  meridiem: 'short',\n})\n\nexport interface ListViewEventRowProps extends MinimalEventProps {\n  timeHeaderId: string\n  eventHeaderId: string\n  dateHeaderId: string\n}\n\nexport class ListViewEventRow extends BaseComponent<ListViewEventRowProps> {\n  render() {\n    let { props, context } = this\n    let { seg, timeHeaderId, eventHeaderId, dateHeaderId } = props\n    let timeFormat = context.options.eventTimeFormat || DEFAULT_TIME_FORMAT\n\n    return (\n      <EventRoot\n        seg={seg}\n        timeText=\"\" // BAD. because of all-day content\n        disableDragging\n        disableResizing\n        defaultContent={() => renderEventInnerContent(seg, context) /* weird */}\n        isPast={props.isPast}\n        isFuture={props.isFuture}\n        isToday={props.isToday}\n        isSelected={props.isSelected}\n        isDragging={props.isDragging}\n        isResizing={props.isResizing}\n        isDateSelecting={props.isDateSelecting}\n      >\n        {(rootElRef, classNames, innerElRef, innerContent, hookProps) => (\n          <tr className={['fc-list-event', hookProps.event.url ? 'fc-event-forced-url' : ''].concat(classNames).join(' ')} ref={rootElRef}>\n            {buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId)}\n            <td aria-hidden className=\"fc-list-event-graphic\">\n              <span className=\"fc-list-event-dot\" style={{ borderColor: hookProps.borderColor || hookProps.backgroundColor }} />\n            </td>\n            <td ref={innerElRef} headers={`${eventHeaderId} ${dateHeaderId}`} className=\"fc-list-event-title\">\n              {innerContent}\n            </td>\n          </tr>\n        )}\n      </EventRoot>\n    )\n  }\n}\n\nfunction renderEventInnerContent(seg: Seg, context: ViewContext) {\n  let interactiveAttrs = getSegAnchorAttrs(seg, context)\n  return (\n    <a {...interactiveAttrs}>\n      {/* TODO: document how whole row become clickable */}\n      {seg.eventRange.def.title}\n    </a>\n  )\n}\n\nfunction buildTimeContent(\n  seg: Seg,\n  timeFormat: DateFormatter,\n  context: ViewContext,\n  timeHeaderId: string,\n  dateHeaderId: string,\n): ComponentChildren {\n  let { options } = context\n\n  if (options.displayEventTime !== false) {\n    let eventDef = seg.eventRange.def\n    let eventInstance = seg.eventRange.instance\n    let doAllDay = false\n    let timeText: string\n\n    if (eventDef.allDay) {\n      doAllDay = true\n    } else if (isMultiDayRange(seg.eventRange.range)) { // TODO: use (!isStart || !isEnd) instead?\n      if (seg.isStart) {\n        timeText = buildSegTimeText(\n          seg,\n          timeFormat,\n          context,\n          null,\n          null,\n          eventInstance.range.start,\n          seg.end,\n        )\n      } else if (seg.isEnd) {\n        timeText = buildSegTimeText(\n          seg,\n          timeFormat,\n          context,\n          null,\n          null,\n          seg.start,\n          eventInstance.range.end,\n        )\n      } else {\n        doAllDay = true\n      }\n    } else {\n      timeText = buildSegTimeText(\n        seg,\n        timeFormat,\n        context,\n      )\n    }\n\n    if (doAllDay) {\n      let hookProps: AllDayContentArg = {\n        text: context.options.allDayText,\n        view: context.viewApi,\n      }\n\n      return (\n        <RenderHook<AllDayContentArg> // needed?\n          hookProps={hookProps}\n          classNames={options.allDayClassNames}\n          content={options.allDayContent}\n          defaultContent={renderAllDayInner}\n          didMount={options.allDayDidMount}\n          willUnmount={options.allDayWillUnmount}\n        >\n          {(rootElRef, classNames, innerElRef, innerContent) => (\n            <td ref={rootElRef} headers={`${timeHeaderId} ${dateHeaderId}`} className={['fc-list-event-time'].concat(classNames).join(' ')}>\n              {innerContent}\n            </td>\n          )}\n        </RenderHook>\n      )\n    }\n\n    return (\n      <td className=\"fc-list-event-time\">\n        {timeText}\n      </td>\n    )\n  }\n\n  return null\n}\n\nfunction renderAllDayInner(hookProps) {\n  return hookProps.text\n}\n", "import {\n  createElement,\n  ViewProps,\n  Scroller,\n  DateMarker,\n  addDays,\n  startOfDay,\n  DateRange,\n  intersectRanges,\n  DateProfile,\n  EventUiHash,\n  EventRenderRange,\n  sliceEventStore,\n  EventStore,\n  memoize,\n  Seg,\n  VNode,\n  sortEventSegs,\n  getSegMeta,\n  NowTimer,\n  ViewRoot,\n  RenderHook,\n  DateComponent,\n  ViewApi,\n  MountArg,\n  getUniqueDomId,\n  formatDayString,\n} from '@fullcalendar/common'\nimport { ListViewHeaderRow } from './ListViewHeaderRow'\nimport { ListViewEventRow } from './ListViewEventRow'\n\nexport interface NoEventsContentArg {\n  text: string\n  view: ViewApi\n}\n\nexport type NoEventsMountArg = MountArg<NoEventsContentArg>\n\n/*\nResponsible for the scroller, and forwarding event-related actions into the \"grid\".\n*/\nexport class ListView extends DateComponent<ViewProps> {\n  private computeDateVars = memoize(computeDateVars)\n  private eventStoreToSegs = memoize(this._eventStoreToSegs)\n  state = {\n    timeHeaderId: getUniqueDomId(),\n    eventHeaderId: getUniqueDomId(),\n    dateHeaderIdRoot: getUniqueDomId(),\n  }\n\n  render() {\n    let { props, context } = this\n\n    let extraClassNames = [\n      'fc-list',\n      context.theme.getClass('table'), // just for the outer border. will be on div\n      context.options.stickyHeaderDates !== false ? 'fc-list-sticky' : '',\n    ]\n\n    let { dayDates, dayRanges } = this.computeDateVars(props.dateProfile)\n    let eventSegs = this.eventStoreToSegs(props.eventStore, props.eventUiBases, dayRanges)\n\n    return (\n      <ViewRoot viewSpec={context.viewSpec} elRef={this.setRootEl}>\n        {(rootElRef, classNames) => (\n          <div ref={rootElRef} className={extraClassNames.concat(classNames).join(' ')}>\n            <Scroller\n              liquid={!props.isHeightAuto}\n              overflowX={props.isHeightAuto ? 'visible' : 'hidden'}\n              overflowY={props.isHeightAuto ? 'visible' : 'auto'}\n            >\n              {eventSegs.length > 0 ?\n                this.renderSegList(eventSegs, dayDates) :\n                this.renderEmptyMessage()}\n            </Scroller>\n          </div>\n        )}\n      </ViewRoot>\n    )\n  }\n\n  setRootEl = (rootEl: HTMLDivElement | null) => {\n    if (rootEl) {\n      this.context.registerInteractiveComponent(this, { // TODO: make aware that it doesn't do Hits\n        el: rootEl,\n      })\n    } else {\n      this.context.unregisterInteractiveComponent(this)\n    }\n  }\n\n  renderEmptyMessage() {\n    let { options, viewApi } = this.context\n    let hookProps: NoEventsContentArg = {\n      text: options.noEventsText,\n      view: viewApi,\n    }\n\n    return (\n      <RenderHook<NoEventsContentArg> // needed???\n        hookProps={hookProps}\n        classNames={options.noEventsClassNames}\n        content={options.noEventsContent}\n        defaultContent={renderNoEventsInner}\n        didMount={options.noEventsDidMount}\n        willUnmount={options.noEventsWillUnmount}\n      >\n        {(rootElRef, classNames, innerElRef, innerContent) => (\n          <div className={['fc-list-empty'].concat(classNames).join(' ')} ref={rootElRef}>\n            <div className=\"fc-list-empty-cushion\" ref={innerElRef}>\n              {innerContent}\n            </div>\n          </div>\n        )}\n      </RenderHook>\n    )\n  }\n\n  renderSegList(allSegs: Seg[], dayDates: DateMarker[]) {\n    let { theme, options } = this.context\n    let { timeHeaderId, eventHeaderId, dateHeaderIdRoot } = this.state\n    let segsByDay = groupSegsByDay(allSegs) // sparse array\n\n    return (\n      <NowTimer unit=\"day\">\n        {(nowDate: DateMarker, todayRange: DateRange) => {\n          let innerNodes: VNode[] = []\n\n          for (let dayIndex = 0; dayIndex < segsByDay.length; dayIndex += 1) {\n            let daySegs = segsByDay[dayIndex]\n\n            if (daySegs) { // sparse array, so might be undefined\n              let dayStr = formatDayString(dayDates[dayIndex])\n              let dateHeaderId = dateHeaderIdRoot + '-' + dayStr\n\n              // append a day header\n              innerNodes.push(\n                <ListViewHeaderRow\n                  key={dayStr}\n                  cellId={dateHeaderId}\n                  dayDate={dayDates[dayIndex]}\n                  todayRange={todayRange}\n                />,\n              )\n\n              daySegs = sortEventSegs(daySegs, options.eventOrder)\n\n              for (let seg of daySegs) {\n                innerNodes.push(\n                  <ListViewEventRow\n                    key={dayStr + ':' + seg.eventRange.instance.instanceId /* are multiple segs for an instanceId */}\n                    seg={seg}\n                    isDragging={false}\n                    isResizing={false}\n                    isDateSelecting={false}\n                    isSelected={false}\n                    timeHeaderId={timeHeaderId}\n                    eventHeaderId={eventHeaderId}\n                    dateHeaderId={dateHeaderId}\n                    {...getSegMeta(seg, todayRange, nowDate)}\n                  />,\n                )\n              }\n            }\n          }\n\n          return (\n            <table className={'fc-list-table ' + theme.getClass('table')}>\n              <thead>\n                <tr>\n                  <th scope=\"col\" id={timeHeaderId}>{options.timeHint}</th>\n                  <th scope=\"col\" aria-hidden />\n                  <th scope=\"col\" id={eventHeaderId}>{options.eventHint}</th>\n                </tr>\n              </thead>\n              <tbody>{innerNodes}</tbody>\n            </table>\n          )\n        }}\n      </NowTimer>\n    )\n  }\n\n  _eventStoreToSegs(eventStore: EventStore, eventUiBases: EventUiHash, dayRanges: DateRange[]): Seg[] {\n    return this.eventRangesToSegs(\n      sliceEventStore(\n        eventStore,\n        eventUiBases,\n        this.props.dateProfile.activeRange,\n        this.context.options.nextDayThreshold,\n      ).fg,\n      dayRanges,\n    )\n  }\n\n  eventRangesToSegs(eventRanges: EventRenderRange[], dayRanges: DateRange[]) {\n    let segs = []\n\n    for (let eventRange of eventRanges) {\n      segs.push(...this.eventRangeToSegs(eventRange, dayRanges))\n    }\n\n    return segs\n  }\n\n  eventRangeToSegs(eventRange: EventRenderRange, dayRanges: DateRange[]) {\n    let { dateEnv } = this.context\n    let { nextDayThreshold } = this.context.options\n    let range = eventRange.range\n    let allDay = eventRange.def.allDay\n    let dayIndex\n    let segRange\n    let seg\n    let segs = []\n\n    for (dayIndex = 0; dayIndex < dayRanges.length; dayIndex += 1) {\n      segRange = intersectRanges(range, dayRanges[dayIndex])\n\n      if (segRange) {\n        seg = {\n          component: this,\n          eventRange,\n          start: segRange.start,\n          end: segRange.end,\n          isStart: eventRange.isStart && segRange.start.valueOf() === range.start.valueOf(),\n          isEnd: eventRange.isEnd && segRange.end.valueOf() === range.end.valueOf(),\n          dayIndex,\n        }\n\n        segs.push(seg)\n\n        // detect when range won't go fully into the next day,\n        // and mutate the latest seg to the be the end.\n        if (\n          !seg.isEnd && !allDay &&\n          dayIndex + 1 < dayRanges.length &&\n          range.end <\n            dateEnv.add(\n              dayRanges[dayIndex + 1].start,\n              nextDayThreshold,\n            )\n        ) {\n          seg.end = range.end\n          seg.isEnd = true\n          break\n        }\n      }\n    }\n\n    return segs\n  }\n}\n\nfunction renderNoEventsInner(hookProps) {\n  return hookProps.text\n}\n\nfunction computeDateVars(dateProfile: DateProfile) {\n  let dayStart = startOfDay(dateProfile.renderRange.start)\n  let viewEnd = dateProfile.renderRange.end\n  let dayDates: DateMarker[] = []\n  let dayRanges: DateRange[] = []\n\n  while (dayStart < viewEnd) {\n    dayDates.push(dayStart)\n\n    dayRanges.push({\n      start: dayStart,\n      end: addDays(dayStart, 1),\n    })\n\n    dayStart = addDays(dayStart, 1)\n  }\n\n  return { dayDates, dayRanges }\n}\n\n// Returns a sparse array of arrays, segs grouped by their dayIndex\nfunction groupSegsByDay(segs): Seg[][] {\n  let segsByDay = [] // sparse array\n  let i\n  let seg\n\n  for (i = 0; i < segs.length; i += 1) {\n    seg = segs[i];\n    (segsByDay[seg.dayIndex] || (segsByDay[seg.dayIndex] = []))\n      .push(seg)\n  }\n\n  return segsByDay\n}\n", "import {\n  identity,\n  Identity,\n  ClassNamesGenerator,\n  CustomContentGenerator,\n  DidMountHandler,\n  WillUnmountHandler,\n  createFormatter,\n  FormatterInput,\n} from '@fullcalendar/common'\n\n// public\nimport {\n  NoEventsContentArg,\n  NoEventsMountArg,\n} from './api-type-deps'\n\nexport const OPTION_REFINERS = {\n  listDayFormat: createFalsableFormatter, // defaults specified in list plugins\n  listDaySideFormat: createFalsableFormatter, // \"\n\n  noEventsClassNames: identity as Identity<ClassNamesGenerator<NoEventsContentArg>>,\n  noEventsContent: identity as Identity<CustomContentGenerator<NoEventsContentArg>>,\n  noEventsDidMount: identity as Identity<DidMountHandler<NoEventsMountArg>>,\n  noEventsWillUnmount: identity as Identity<WillUnmountHandler<NoEventsMountArg>>,\n\n  // noEventsText is defined in base options\n}\n\nfunction createFalsableFormatter(input: FormatterInput | false) {\n  return input === false ? null : createFormatter(input)\n}\n", "import { createPlugin } from '@fullcalendar/common'\nimport { ListView } from './ListView'\nimport { OPTION_REFINERS } from './options'\nimport './options-declare'\nimport './main.css'\n\nexport { ListView }\nexport * from './api-type-deps'\n\nexport default createPlugin({\n  optionRefiners: OPTION_REFINERS,\n  views: {\n\n    list: {\n      component: ListView,\n      buttonTextKey: 'list', // what to lookup in locale files\n      listDayFormat: { month: 'long', day: 'numeric', year: 'numeric' }, // like \"January 1, 2016\"\n    },\n\n    listDay: {\n      type: 'list',\n      duration: { days: 1 },\n      listDayFormat: { weekday: 'long' }, // day-of-week is all we need. full date is probably in headerToolbar\n    },\n\n    listWeek: {\n      type: 'list',\n      duration: { weeks: 1 },\n      listDayFormat: { weekday: 'long' }, // day-of-week is more important\n      listDaySideFormat: { month: 'long', day: 'numeric', year: 'numeric' },\n    },\n\n    listMonth: {\n      type: 'list',\n      duration: { month: 1 },\n      listDaySideFormat: { weekday: 'long' }, // day-of-week is nice-to-have\n    },\n\n    listYear: {\n      type: 'list',\n      duration: { year: 1 },\n      listDaySideFormat: { weekday: 'long' }, // day-of-week is nice-to-have\n    },\n\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;AAiBA;IAAuC,qCAAqC;IAA5E;QAAA,qEA2DC;QA1DC,WAAK,GAAG;YACN,MAAM,EAAE,cAAc,EAAE;SACzB,CAAA;;KAwDF;IAtDC,kCAAM,GAAN;QACM,IAAA,KAAuC,IAAI,CAAC,OAAO,EAAjD,KAAK,WAAA,EAAE,OAAO,aAAA,EAAE,OAAO,aAAA,EAAE,OAAO,aAAiB,CAAA;QACnD,IAAA,KAAkC,IAAI,CAAC,KAAK,EAA1C,MAAM,YAAA,EAAE,OAAO,aAAA,EAAE,UAAU,gBAAe,CAAA;QAC1C,IAAA,MAAM,GAAK,IAAI,CAAC,KAAK,OAAf,CAAe;QAC3B,IAAI,OAAO,GAAG,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;;QAG9C,IAAI,IAAI,GAAG,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,CAAA;;QAGtF,IAAI,QAAQ,GAAG,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAA;QAElG,IAAI,SAAS,cACX,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAC7B,IAAI,EAAE,OAAO,EACb,MAAM,QAAA;YACN,IAAI,MAAA;YACJ,QAAQ,UAAA,EACR,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,EACtD,gBAAgB,EAAE,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,IACrE,OAAO,CACX,CAAA;QAED,IAAI,UAAU,GAAG,CAAC,aAAa,CAAC,CAAC,MAAM,CACrC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CACjC,CAAA;;QAGD,QACE,cAAC,UAAU,IACT,SAAS,EAAE,SAAS,EACpB,UAAU,EAAE,OAAO,CAAC,mBAAmB,EACvC,OAAO,EAAE,OAAO,CAAC,gBAAgB,EACjC,cAAc,EAAE,kBAAkB,EAClC,QAAQ,EAAE,OAAO,CAAC,iBAAiB,EACnC,WAAW,EAAE,OAAO,CAAC,oBAAoB,IAExC,UAAC,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,YAAY,IAAK,QAC1D,sBACE,GAAG,EAAE,SAAS,EACd,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,eAC7C,eAAe,CAAC,OAAO,CAAC;YAGnC,sBAAI,KAAK,EAAC,UAAU,EAAC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,qBAAmB,MAAM;gBAClE,uBAAK,SAAS,EAAE,sBAAsB,GAAG,KAAK,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,GAAG,EAAE,UAAU,IACxF,YAAY,CACT,CACH,CACF,IACN,CACU,EACd;KACF;IACH,wBAAC;AAAD,CA3DA,CAAuC,aAAa,GA2DnD;AAED,SAAS,kBAAkB,CAAC,KAAgB;IAC1C,QACE,cAAC,QAAQ;QACN,KAAK,CAAC,IAAI,KACT,8BAAG,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,SAAS,EAAC,kBAAkB,IAAK,KAAK,CAAC,YAAY,GACrE,KAAK,CAAC,IAAI,CACT,CACL;QACA,KAAK,CAAC,QAAQ,iCACb,mDAAe,SAAS,EAAC,uBAAuB,IAAK,KAAK,CAAC,gBAAgB,GACxE,KAAK,CAAC,QAAQ,CACb,CACL,CACQ,EACZ;AACH;;ACxFA,IAAM,mBAAmB,GAAG,eAAe,CAAC;IAC1C,IAAI,EAAE,SAAS;IACf,MAAM,EAAE,SAAS;IACjB,QAAQ,EAAE,OAAO;CAClB,CAAC,CAAA;AAQF;IAAsC,oCAAoC;IAA1E;;KAmCC;IAlCC,iCAAM,GAAN;QACM,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QACvB,IAAA,GAAG,GAAgD,KAAK,IAArD,EAAE,YAAY,GAAkC,KAAK,aAAvC,EAAE,aAAa,GAAmB,KAAK,cAAxB,EAAE,YAAY,GAAK,KAAK,aAAV,CAAU;QAC9D,IAAI,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,IAAI,mBAAmB,CAAA;QAEvE,QACE,cAAC,SAAS,IACR,GAAG,EAAE,GAAG,EACR,QAAQ,EAAC,EAAE;cACX,eAAe,QACf,eAAe,QACf,cAAc,EAAE,cAAM,OAAA,uBAAuB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAA,cAC3D,MAAM,EAAE,KAAK,CAAC,MAAM,EACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ,EACxB,OAAO,EAAE,KAAK,CAAC,OAAO,EACtB,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,UAAU,EAAE,KAAK,CAAC,UAAU,EAC5B,eAAe,EAAE,KAAK,CAAC,eAAe,IAErC,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,IAAK,QAC/D,sBAAI,SAAS,EAAE,CAAC,eAAe,EAAE,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,qBAAqB,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,SAAS;YAC5H,gBAAgB,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC;YACvE,2CAAgB,SAAS,EAAC,uBAAuB;gBAC/C,wBAAM,SAAS,EAAC,mBAAmB,EAAC,KAAK,EAAE,EAAE,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,eAAe,EAAE,GAAI,CAC/G;YACL,sBAAI,GAAG,EAAE,UAAU,EAAE,OAAO,EAAK,aAAa,SAAI,YAAc,EAAE,SAAS,EAAC,qBAAqB,IAC9F,YAAY,CACV,CACF,IACN,CACS,EACb;KACF;IACH,uBAAC;AAAD,CAnCA,CAAsC,aAAa,GAmClD;AAED,SAAS,uBAAuB,CAAC,GAAQ,EAAE,OAAoB;IAC7D,IAAI,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IACtD,QACE,gCAAO,gBAAgB,GAEpB,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CACvB,EACL;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,GAAQ,EACR,UAAyB,EACzB,OAAoB,EACpB,YAAoB,EACpB,YAAoB;IAEd,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAY;IAEzB,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,EAAE;QACtC,IAAI,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAA;QACjC,IAAI,aAAa,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAA;QAC3C,IAAI,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI,QAAQ,SAAQ,CAAA;QAEpB,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,QAAQ,GAAG,IAAI,CAAA;SAChB;aAAM,IAAI,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;YAChD,IAAI,GAAG,CAAC,OAAO,EAAE;gBACf,QAAQ,GAAG,gBAAgB,CACzB,GAAG,EACH,UAAU,EACV,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,aAAa,CAAC,KAAK,CAAC,KAAK,EACzB,GAAG,CAAC,GAAG,CACR,CAAA;aACF;iBAAM,IAAI,GAAG,CAAC,KAAK,EAAE;gBACpB,QAAQ,GAAG,gBAAgB,CACzB,GAAG,EACH,UAAU,EACV,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,GAAG,CAAC,KAAK,EACT,aAAa,CAAC,KAAK,CAAC,GAAG,CACxB,CAAA;aACF;iBAAM;gBACL,QAAQ,GAAG,IAAI,CAAA;aAChB;SACF;aAAM;YACL,QAAQ,GAAG,gBAAgB,CACzB,GAAG,EACH,UAAU,EACV,OAAO,CACR,CAAA;SACF;QAED,IAAI,QAAQ,EAAE;YACZ,IAAI,SAAS,GAAqB;gBAChC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU;gBAChC,IAAI,EAAE,OAAO,CAAC,OAAO;aACtB,CAAA;YAED,QACE,cAAC,UAAU,IACT,SAAS,EAAE,SAAS,EACpB,UAAU,EAAE,OAAO,CAAC,gBAAgB,EACpC,OAAO,EAAE,OAAO,CAAC,aAAa,EAC9B,cAAc,EAAE,iBAAiB,EACjC,QAAQ,EAAE,OAAO,CAAC,cAAc,EAChC,WAAW,EAAE,OAAO,CAAC,iBAAiB,IAErC,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,IAAK,QACpD,sBAAI,GAAG,EAAE,SAAS,EAAE,OAAO,EAAK,YAAY,SAAI,YAAc,EAAE,SAAS,EAAE,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAC3H,YAAY,CACV,IACN,CACU,EACd;SACF;QAED,QACE,sBAAI,SAAS,EAAC,oBAAoB,IAC/B,QAAQ,CACN,EACN;KACF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAS;IAClC,OAAO,SAAS,CAAC,IAAI,CAAA;AACvB;;AC/GA;;;;IAG8B,4BAAwB;IAAtD;QAAA,qEAkNC;QAjNS,qBAAe,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;QAC1C,sBAAgB,GAAG,OAAO,CAAC,KAAI,CAAC,iBAAiB,CAAC,CAAA;QAC1D,WAAK,GAAG;YACN,YAAY,EAAE,cAAc,EAAE;YAC9B,aAAa,EAAE,cAAc,EAAE;YAC/B,gBAAgB,EAAE,cAAc,EAAE;SACnC,CAAA;QAiCD,eAAS,GAAG,UAAC,MAA6B;YACxC,IAAI,MAAM,EAAE;gBACV,KAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC,KAAI,EAAE;oBAC9C,EAAE,EAAE,MAAM;iBACX,CAAC,CAAA;aACH;iBAAM;gBACL,KAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,KAAI,CAAC,CAAA;aAClD;SACF,CAAA;;KAkKF;IAzMC,yBAAM,GAAN;QAAA,iBA6BC;QA5BK,IAAA,KAAqB,IAAI,EAAvB,KAAK,WAAA,EAAE,OAAO,aAAS,CAAA;QAE7B,IAAI,eAAe,GAAG;YACpB,SAAS;YACT,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC/B,OAAO,CAAC,OAAO,CAAC,iBAAiB,KAAK,KAAK,GAAG,gBAAgB,GAAG,EAAE;SACpE,CAAA;QAEG,IAAA,KAA0B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,EAA/D,QAAQ,cAAA,EAAE,SAAS,eAA4C,CAAA;QACrE,IAAI,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,EAAE,SAAS,CAAC,CAAA;QAEtF,QACE,cAAC,QAAQ,IAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,IACxD,UAAC,SAAS,EAAE,UAAU,IAAK,QAC1B,uBAAK,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YAC1E,cAAC,QAAQ,IACP,MAAM,EAAE,CAAC,KAAK,CAAC,YAAY,EAC3B,SAAS,EAAE,KAAK,CAAC,YAAY,GAAG,SAAS,GAAG,QAAQ,EACpD,SAAS,EAAE,KAAK,CAAC,YAAY,GAAG,SAAS,GAAG,MAAM,IAEjD,SAAS,CAAC,MAAM,GAAG,CAAC;gBACnB,KAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC;gBACvC,KAAI,CAAC,kBAAkB,EAAE,CAClB,CACP,IACP,CACQ,EACZ;KACF;IAYD,qCAAkB,GAAlB;QACM,IAAA,KAAuB,IAAI,CAAC,OAAO,EAAjC,OAAO,aAAA,EAAE,OAAO,aAAiB,CAAA;QACvC,IAAI,SAAS,GAAuB;YAClC,IAAI,EAAE,OAAO,CAAC,YAAY;YAC1B,IAAI,EAAE,OAAO;SACd,CAAA;QAED,QACE,cAAC,UAAU,IACT,SAAS,EAAE,SAAS,EACpB,UAAU,EAAE,OAAO,CAAC,kBAAkB,EACtC,OAAO,EAAE,OAAO,CAAC,eAAe,EAChC,cAAc,EAAE,mBAAmB,EACnC,QAAQ,EAAE,OAAO,CAAC,gBAAgB,EAClC,WAAW,EAAE,OAAO,CAAC,mBAAmB,IAEvC,UAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,IAAK,QACpD,uBAAK,SAAS,EAAE,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,SAAS;YAC5E,uBAAK,SAAS,EAAC,uBAAuB,EAAC,GAAG,EAAE,UAAU,IACnD,YAAY,CACT,CACF,IACP,CACU,EACd;KACF;IAED,gCAAa,GAAb,UAAc,OAAc,EAAE,QAAsB;QAC9C,IAAA,KAAqB,IAAI,CAAC,OAAO,EAA/B,KAAK,WAAA,EAAE,OAAO,aAAiB,CAAA;QACjC,IAAA,KAAoD,IAAI,CAAC,KAAK,EAA5D,YAAY,kBAAA,EAAE,aAAa,mBAAA,EAAE,gBAAgB,sBAAe,CAAA;QAClE,IAAI,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;QAEvC,QACE,cAAC,QAAQ,IAAC,IAAI,EAAC,KAAK,IACjB,UAAC,OAAmB,EAAE,UAAqB;YAC1C,IAAI,UAAU,GAAY,EAAE,CAAA;YAE5B,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,EAAE;gBACjE,IAAI,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAA;gBAEjC,IAAI,OAAO,EAAE;oBACX,IAAI,MAAM,GAAG,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA;oBAChD,IAAI,YAAY,GAAG,gBAAgB,GAAG,GAAG,GAAG,MAAM,CAAA;;oBAGlD,UAAU,CAAC,IAAI,CACb,cAAC,iBAAiB,IAChB,GAAG,EAAE,MAAM,EACX,MAAM,EAAE,YAAY,EACpB,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAC3B,UAAU,EAAE,UAAU,GACtB,CACH,CAAA;oBAED,OAAO,GAAG,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAA;oBAEpD,KAAgB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;wBAApB,IAAI,GAAG,gBAAA;wBACV,UAAU,CAAC,IAAI,CACb,cAAC,gBAAgB,aACf,GAAG,EAAE,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,4CACtD,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,KAAK,EACjB,UAAU,EAAE,KAAK,EACjB,eAAe,EAAE,KAAK,EACtB,UAAU,EAAE,KAAK,EACjB,YAAY,EAAE,YAAY,EAC1B,aAAa,EAAE,aAAa,EAC5B,YAAY,EAAE,YAAY,IACtB,UAAU,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,EACxC,CACH,CAAA;qBACF;iBACF;aACF;YAED,QACE,yBAAO,SAAS,EAAE,gBAAgB,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC1D;oBACE;wBACE,sBAAI,KAAK,EAAC,KAAK,EAAC,EAAE,EAAE,YAAY,IAAG,OAAO,CAAC,QAAQ,CAAM;wBACzD,sBAAI,KAAK,EAAC,KAAK,wBAAe;wBAC9B,sBAAI,KAAK,EAAC,KAAK,EAAC,EAAE,EAAE,aAAa,IAAG,OAAO,CAAC,SAAS,CAAM,CACxD,CACC;gBACR,6BAAQ,UAAU,CAAS,CACrB,EACT;SACF,CACQ,EACZ;KACF;IAED,oCAAiB,GAAjB,UAAkB,UAAsB,EAAE,YAAyB,EAAE,SAAsB;QACzF,OAAO,IAAI,CAAC,iBAAiB,CAC3B,eAAe,CACb,UAAU,EACV,YAAY,EACZ,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,WAAW,EAClC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CACtC,CAAC,EAAE,EACJ,SAAS,CACV,CAAA;KACF;IAED,oCAAiB,GAAjB,UAAkB,WAA+B,EAAE,SAAsB;QACvE,IAAI,IAAI,GAAG,EAAE,CAAA;QAEb,KAAuB,UAAW,EAAX,2BAAW,EAAX,yBAAW,EAAX,IAAW,EAAE;YAA/B,IAAI,UAAU,oBAAA;YACjB,IAAI,CAAC,IAAI,OAAT,IAAI,EAAS,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,SAAS,CAAC,EAAC;SAC3D;QAED,OAAO,IAAI,CAAA;KACZ;IAED,mCAAgB,GAAhB,UAAiB,UAA4B,EAAE,SAAsB;QAC7D,IAAA,OAAO,GAAK,IAAI,CAAC,OAAO,QAAjB,CAAiB;QACxB,IAAA,gBAAgB,GAAK,IAAI,CAAC,OAAO,CAAC,OAAO,iBAAzB,CAAyB;QAC/C,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;QAC5B,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAA;QAClC,IAAI,QAAQ,CAAA;QACZ,IAAI,QAAQ,CAAA;QACZ,IAAI,GAAG,CAAA;QACP,IAAI,IAAI,GAAG,EAAE,CAAA;QAEb,KAAK,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,EAAE;YAC7D,QAAQ,GAAG,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAA;YAEtD,IAAI,QAAQ,EAAE;gBACZ,GAAG,GAAG;oBACJ,SAAS,EAAE,IAAI;oBACf,UAAU,YAAA;oBACV,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,GAAG,EAAE,QAAQ,CAAC,GAAG;oBACjB,OAAO,EAAE,UAAU,CAAC,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE;oBACjF,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE;oBACzE,QAAQ,UAAA;iBACT,CAAA;gBAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;;;gBAId,IACE,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM;oBACrB,QAAQ,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM;oBAC/B,KAAK,CAAC,GAAG;wBACP,OAAO,CAAC,GAAG,CACT,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAK,EAC7B,gBAAgB,CACjB,EACH;oBACA,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;oBACnB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAA;oBAChB,MAAK;iBACN;aACF;SACF;QAED,OAAO,IAAI,CAAA;KACZ;IACH,eAAC;AAAD,CAlNA,CAA8B,aAAa,GAkN1C;AAED,SAAS,mBAAmB,CAAC,SAAS;IACpC,OAAO,SAAS,CAAC,IAAI,CAAA;AACvB,CAAC;AAED,SAAS,eAAe,CAAC,WAAwB;IAC/C,IAAI,QAAQ,GAAG,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IACxD,IAAI,OAAO,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAA;IACzC,IAAI,QAAQ,GAAiB,EAAE,CAAA;IAC/B,IAAI,SAAS,GAAgB,EAAE,CAAA;IAE/B,OAAO,QAAQ,GAAG,OAAO,EAAE;QACzB,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAEvB,SAAS,CAAC,IAAI,CAAC;YACb,KAAK,EAAE,QAAQ;YACf,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC1B,CAAC,CAAA;QAEF,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;KAChC;IAED,OAAO,EAAE,QAAQ,UAAA,EAAE,SAAS,WAAA,EAAE,CAAA;AAChC,CAAC;AAED;AACA,SAAS,cAAc,CAAC,IAAI;IAC1B,IAAI,SAAS,GAAG,EAAE,CAAA;IAClB,IAAI,CAAC,CAAA;IACL,IAAI,GAAG,CAAA;IAEP,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;QACnC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;aACvD,IAAI,CAAC,GAAG,CAAC,CAAA;KACb;IAED,OAAO,SAAS,CAAA;AAClB;;ACjRO,IAAM,eAAe,GAAG;IAC7B,aAAa,EAAE,uBAAuB;IACtC,iBAAiB,EAAE,uBAAuB;IAE1C,kBAAkB,EAAE,QAA6D;IACjF,eAAe,EAAE,QAAgE;IACjF,gBAAgB,EAAE,QAAuD;IACzE,mBAAmB,EAAE,QAA0D;;CAGhF,CAAA;AAED,SAAS,uBAAuB,CAAC,KAA6B;IAC5D,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,CAAA;AACxD;;ACtBA,WAAe,YAAY,CAAC;IAC1B,cAAc,EAAE,eAAe;IAC/B,KAAK,EAAE;QAEL,IAAI,EAAE;YACJ,SAAS,EAAE,QAAQ;YACnB,aAAa,EAAE,MAAM;YACrB,aAAa,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;SAClE;QAED,OAAO,EAAE;YACP,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;YACrB,aAAa,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;SACnC;QAED,QAAQ,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACtB,aAAa,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;YAClC,iBAAiB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;SACtE;QAED,SAAS,EAAE;YACT,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;YACtB,iBAAiB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;SACvC;QAED,QAAQ,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;YACrB,iBAAiB,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE;SACvC;KAEF;CACF,CAAC;;;;;"}