{"name": "@fullcalendar/core", "version": "5.10.1", "title": "FullCalendar Core Package", "description": "Provides core functionality, including the Calendar class", "docs": "https://fullcalendar.io/docs/initialize-es6", "dependencies": {"@fullcalendar/common": "~5.10.1", "preact": "^10.0.5", "tslib": "^2.1.0"}, "main": "main.cjs.js", "module": "main.js", "types": "main.d.ts", "jsdelivr": "main.global.min.js", "browserGlobal": "FullCalendar", "sideEffects": true, "homepage": "https://fullcalendar.io/", "bugs": "https://fullcalendar.io/reporting-bugs", "repository": {"type": "git", "url": "https://github.com/fullcalendar/fullcalendar.git", "homepage": "https://github.com/fullcalendar/fullcalendar"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://arshaw.com/"}, "devDependencies": {"@fullcalendar/core-preact": "5.10.1"}}