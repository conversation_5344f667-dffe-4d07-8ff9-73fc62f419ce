{"version": 3, "file": "main.js", "sources": ["src/dnd/PointerDragging.ts", "src/dnd/ElementMirror.ts", "src/ScrollGeomCache.ts", "src/ElementScrollGeomCache.ts", "src/WindowScrollGeomCache.ts", "src/dnd/AutoScroller.ts", "src/dnd/FeaturefulElementDragging.ts", "src/OffsetTracker.ts", "src/interactions/HitDragging.ts", "src/utils.ts", "src/interactions/DateClicking.ts", "src/interactions/DateSelecting.ts", "src/interactions/EventDragging.ts", "src/interactions/EventResizing.ts", "src/interactions/UnselectAuto.ts", "src/options.ts", "src/interactions-external/ExternalElementDragging.ts", "src/interactions-external/ExternalDraggable.ts", "src/interactions-external/InferredElementDragging.ts", "src/interactions-external/ThirdPartyDraggable.ts", "src/main.ts"], "sourcesContent": ["import { config, elementClosest, Emitter, PointerDragEvent } from '@fullcalendar/common'\n\nconfig.touchMouseIgnoreWait = 500\n\nlet ignoreMouseDepth = 0\nlet listenerCnt = 0\nlet isWindowTouchMoveCancelled = false\n\n/*\nUses a \"pointer\" abstraction, which monitors UI events for both mouse and touch.\nTracks when the pointer \"drags\" on a certain element, meaning down+move+up.\n\nAlso, tracks if there was touch-scrolling.\nAlso, can prevent touch-scrolling from happening.\nAlso, can fire pointermove events when scrolling happens underneath, even when no real pointer movement.\n\nemits:\n- pointerdown\n- pointermove\n- pointerup\n*/\nexport class PointerDragging {\n  containerEl: EventTarget\n  subjectEl: HTMLElement | null = null\n  emitter: Emitter<any>\n\n  // options that can be directly assigned by caller\n  selector: string = '' // will cause subjectEl in all emitted events to be this element\n  handleSelector: string = ''\n  shouldIgnoreMove: boolean = false\n  shouldWatchScroll: boolean = true // for simulating pointermove on scroll\n\n  // internal states\n  isDragging: boolean = false\n  isTouchDragging: boolean = false\n  wasTouchScroll: boolean = false\n  origPageX: number\n  origPageY: number\n  prevPageX: number\n  prevPageY: number\n  prevScrollX: number // at time of last pointer pageX/pageY capture\n  prevScrollY: number // \"\n\n  constructor(containerEl: EventTarget) {\n    this.containerEl = containerEl\n    this.emitter = new Emitter()\n    containerEl.addEventListener('mousedown', this.handleMouseDown as EventListener)\n    containerEl.addEventListener('touchstart', this.handleTouchStart as EventListener, { passive: true })\n    listenerCreated()\n  }\n\n  destroy() {\n    this.containerEl.removeEventListener('mousedown', this.handleMouseDown as EventListener)\n    this.containerEl.removeEventListener('touchstart', this.handleTouchStart as EventListener, { passive: true } as AddEventListenerOptions)\n    listenerDestroyed()\n  }\n\n  tryStart(ev: UIEvent): boolean {\n    let subjectEl = this.querySubjectEl(ev)\n    let downEl = ev.target as HTMLElement\n\n    if (\n      subjectEl &&\n      (!this.handleSelector || elementClosest(downEl, this.handleSelector))\n    ) {\n      this.subjectEl = subjectEl\n      this.isDragging = true // do this first so cancelTouchScroll will work\n      this.wasTouchScroll = false\n\n      return true\n    }\n\n    return false\n  }\n\n  cleanup() {\n    isWindowTouchMoveCancelled = false\n    this.isDragging = false\n    this.subjectEl = null\n    // keep wasTouchScroll around for later access\n    this.destroyScrollWatch()\n  }\n\n  querySubjectEl(ev: UIEvent): HTMLElement {\n    if (this.selector) {\n      return elementClosest(ev.target as HTMLElement, this.selector)\n    }\n    return this.containerEl as HTMLElement\n  }\n\n  // Mouse\n  // ----------------------------------------------------------------------------------------------------\n\n  handleMouseDown = (ev: MouseEvent) => {\n    if (\n      !this.shouldIgnoreMouse() &&\n      isPrimaryMouseButton(ev) &&\n      this.tryStart(ev)\n    ) {\n      let pev = this.createEventFromMouse(ev, true)\n      this.emitter.trigger('pointerdown', pev)\n      this.initScrollWatch(pev)\n\n      if (!this.shouldIgnoreMove) {\n        document.addEventListener('mousemove', this.handleMouseMove)\n      }\n\n      document.addEventListener('mouseup', this.handleMouseUp)\n    }\n  }\n\n  handleMouseMove = (ev: MouseEvent) => {\n    let pev = this.createEventFromMouse(ev)\n    this.recordCoords(pev)\n    this.emitter.trigger('pointermove', pev)\n  }\n\n  handleMouseUp = (ev: MouseEvent) => {\n    document.removeEventListener('mousemove', this.handleMouseMove)\n    document.removeEventListener('mouseup', this.handleMouseUp)\n\n    this.emitter.trigger('pointerup', this.createEventFromMouse(ev))\n\n    this.cleanup() // call last so that pointerup has access to props\n  }\n\n  shouldIgnoreMouse() {\n    return ignoreMouseDepth || this.isTouchDragging\n  }\n\n  // Touch\n  // ----------------------------------------------------------------------------------------------------\n\n  handleTouchStart = (ev: TouchEvent) => {\n    if (this.tryStart(ev)) {\n      this.isTouchDragging = true\n\n      let pev = this.createEventFromTouch(ev, true)\n      this.emitter.trigger('pointerdown', pev)\n      this.initScrollWatch(pev)\n\n      // unlike mouse, need to attach to target, not document\n      // https://stackoverflow.com/a/45760014\n      let targetEl = ev.target as HTMLElement\n\n      if (!this.shouldIgnoreMove) {\n        targetEl.addEventListener('touchmove', this.handleTouchMove)\n      }\n\n      targetEl.addEventListener('touchend', this.handleTouchEnd)\n      targetEl.addEventListener('touchcancel', this.handleTouchEnd) // treat it as a touch end\n\n      // attach a handler to get called when ANY scroll action happens on the page.\n      // this was impossible to do with normal on/off because 'scroll' doesn't bubble.\n      // http://stackoverflow.com/a/32954565/96342\n      window.addEventListener(\n        'scroll',\n        this.handleTouchScroll,\n        true, // useCapture\n      )\n    }\n  }\n\n  handleTouchMove = (ev: TouchEvent) => {\n    let pev = this.createEventFromTouch(ev)\n    this.recordCoords(pev)\n    this.emitter.trigger('pointermove', pev)\n  }\n\n  handleTouchEnd = (ev: TouchEvent) => {\n    if (this.isDragging) { // done to guard against touchend followed by touchcancel\n      let targetEl = ev.target as HTMLElement\n\n      targetEl.removeEventListener('touchmove', this.handleTouchMove)\n      targetEl.removeEventListener('touchend', this.handleTouchEnd)\n      targetEl.removeEventListener('touchcancel', this.handleTouchEnd)\n      window.removeEventListener('scroll', this.handleTouchScroll, true) // useCaptured=true\n\n      this.emitter.trigger('pointerup', this.createEventFromTouch(ev))\n\n      this.cleanup() // call last so that pointerup has access to props\n      this.isTouchDragging = false\n      startIgnoringMouse()\n    }\n  }\n\n  handleTouchScroll = () => {\n    this.wasTouchScroll = true\n  }\n\n  // can be called by user of this class, to cancel touch-based scrolling for the current drag\n  cancelTouchScroll() {\n    if (this.isDragging) {\n      isWindowTouchMoveCancelled = true\n    }\n  }\n\n  // Scrolling that simulates pointermoves\n  // ----------------------------------------------------------------------------------------------------\n\n  initScrollWatch(ev: PointerDragEvent) {\n    if (this.shouldWatchScroll) {\n      this.recordCoords(ev)\n      window.addEventListener('scroll', this.handleScroll, true) // useCapture=true\n    }\n  }\n\n  recordCoords(ev: PointerDragEvent) {\n    if (this.shouldWatchScroll) {\n      this.prevPageX = (ev as any).pageX\n      this.prevPageY = (ev as any).pageY\n      this.prevScrollX = window.pageXOffset\n      this.prevScrollY = window.pageYOffset\n    }\n  }\n\n  handleScroll = (ev: UIEvent) => {\n    if (!this.shouldIgnoreMove) {\n      let pageX = (window.pageXOffset - this.prevScrollX) + this.prevPageX\n      let pageY = (window.pageYOffset - this.prevScrollY) + this.prevPageY\n\n      this.emitter.trigger('pointermove', {\n        origEvent: ev,\n        isTouch: this.isTouchDragging,\n        subjectEl: this.subjectEl,\n        pageX,\n        pageY,\n        deltaX: pageX - this.origPageX,\n        deltaY: pageY - this.origPageY,\n      } as PointerDragEvent)\n    }\n  }\n\n  destroyScrollWatch() {\n    if (this.shouldWatchScroll) {\n      window.removeEventListener('scroll', this.handleScroll, true) // useCaptured=true\n    }\n  }\n\n  // Event Normalization\n  // ----------------------------------------------------------------------------------------------------\n\n  createEventFromMouse(ev: MouseEvent, isFirst?: boolean): PointerDragEvent {\n    let deltaX = 0\n    let deltaY = 0\n\n    // TODO: repeat code\n    if (isFirst) {\n      this.origPageX = ev.pageX\n      this.origPageY = ev.pageY\n    } else {\n      deltaX = ev.pageX - this.origPageX\n      deltaY = ev.pageY - this.origPageY\n    }\n\n    return {\n      origEvent: ev,\n      isTouch: false,\n      subjectEl: this.subjectEl,\n      pageX: ev.pageX,\n      pageY: ev.pageY,\n      deltaX,\n      deltaY,\n    }\n  }\n\n  createEventFromTouch(ev: TouchEvent, isFirst?: boolean): PointerDragEvent {\n    let touches = ev.touches\n    let pageX\n    let pageY\n    let deltaX = 0\n    let deltaY = 0\n\n    // if touch coords available, prefer,\n    // because FF would give bad ev.pageX ev.pageY\n    if (touches && touches.length) {\n      pageX = touches[0].pageX\n      pageY = touches[0].pageY\n    } else {\n      pageX = (ev as any).pageX\n      pageY = (ev as any).pageY\n    }\n\n    // TODO: repeat code\n    if (isFirst) {\n      this.origPageX = pageX\n      this.origPageY = pageY\n    } else {\n      deltaX = pageX - this.origPageX\n      deltaY = pageY - this.origPageY\n    }\n\n    return {\n      origEvent: ev,\n      isTouch: true,\n      subjectEl: this.subjectEl,\n      pageX,\n      pageY,\n      deltaX,\n      deltaY,\n    }\n  }\n}\n\n// Returns a boolean whether this was a left mouse click and no ctrl key (which means right click on Mac)\nfunction isPrimaryMouseButton(ev: MouseEvent) {\n  return ev.button === 0 && !ev.ctrlKey\n}\n\n// Ignoring fake mouse events generated by touch\n// ----------------------------------------------------------------------------------------------------\n\nfunction startIgnoringMouse() { // can be made non-class function\n  ignoreMouseDepth += 1\n\n  setTimeout(() => {\n    ignoreMouseDepth -= 1\n  }, config.touchMouseIgnoreWait)\n}\n\n// We want to attach touchmove as early as possible for Safari\n// ----------------------------------------------------------------------------------------------------\n\nfunction listenerCreated() {\n  listenerCnt += 1\n\n  if (listenerCnt === 1) {\n    window.addEventListener('touchmove', onWindowTouchMove, { passive: false })\n  }\n}\n\nfunction listenerDestroyed() {\n  listenerCnt -= 1\n\n  if (!listenerCnt) {\n    window.removeEventListener('touchmove', onWindowTouchMove, { passive: false } as AddEventListenerOptions)\n  }\n}\n\nfunction onWindowTouchMove(ev: UIEvent) {\n  if (isWindowTouchMoveCancelled) {\n    ev.preventDefault()\n  }\n}\n", "import { removeElement, applyStyle, whenTransitionDone, Rect } from '@fullcalendar/common'\n\n/*\nAn effect in which an element follows the movement of a pointer across the screen.\nThe moving element is a clone of some other element.\nMust call start + handleMove + stop.\n*/\nexport class ElementMirror {\n  isVisible: boolean = false // must be explicitly enabled\n  origScreenX?: number\n  origScreenY?: number\n  deltaX?: number\n  deltaY?: number\n  sourceEl: HTMLElement | null = null\n  mirrorEl: HTMLElement | null = null\n  sourceElRect: Rect | null = null // screen coords relative to viewport\n\n  // options that can be set directly by caller\n  parentNode: HTMLElement = document.body // HIGHLY SUGGESTED to set this to sidestep ShadowDOM issues\n  zIndex: number = 9999\n  revertDuration: number = 0\n\n  start(sourceEl: HTMLElement, pageX: number, pageY: number) {\n    this.sourceEl = sourceEl\n    this.sourceElRect = this.sourceEl.getBoundingClientRect()\n    this.origScreenX = pageX - window.pageXOffset\n    this.origScreenY = pageY - window.pageYOffset\n    this.deltaX = 0\n    this.deltaY = 0\n    this.updateElPosition()\n  }\n\n  handleMove(pageX: number, pageY: number) {\n    this.deltaX = (pageX - window.pageXOffset) - this.origScreenX!\n    this.deltaY = (pageY - window.pageYOffset) - this.origScreenY!\n    this.updateElPosition()\n  }\n\n  // can be called before start\n  setIsVisible(bool: boolean) {\n    if (bool) {\n      if (!this.isVisible) {\n        if (this.mirrorEl) {\n          this.mirrorEl.style.display = ''\n        }\n\n        this.isVisible = bool // needs to happen before updateElPosition\n        this.updateElPosition() // because was not updating the position while invisible\n      }\n    } else if (this.isVisible) {\n      if (this.mirrorEl) {\n        this.mirrorEl.style.display = 'none'\n      }\n\n      this.isVisible = bool\n    }\n  }\n\n  // always async\n  stop(needsRevertAnimation: boolean, callback: () => void) {\n    let done = () => {\n      this.cleanup()\n      callback()\n    }\n\n    if (\n      needsRevertAnimation &&\n      this.mirrorEl &&\n      this.isVisible &&\n      this.revertDuration && // if 0, transition won't work\n      (this.deltaX || this.deltaY) // if same coords, transition won't work\n    ) {\n      this.doRevertAnimation(done, this.revertDuration)\n    } else {\n      setTimeout(done, 0)\n    }\n  }\n\n  doRevertAnimation(callback: () => void, revertDuration: number) {\n    let mirrorEl = this.mirrorEl!\n    let finalSourceElRect = this.sourceEl!.getBoundingClientRect() // because autoscrolling might have happened\n\n    mirrorEl.style.transition =\n      'top ' + revertDuration + 'ms,' +\n      'left ' + revertDuration + 'ms'\n\n    applyStyle(mirrorEl, {\n      left: finalSourceElRect.left,\n      top: finalSourceElRect.top,\n    })\n\n    whenTransitionDone(mirrorEl, () => {\n      mirrorEl.style.transition = ''\n      callback()\n    })\n  }\n\n  cleanup() {\n    if (this.mirrorEl) {\n      removeElement(this.mirrorEl)\n      this.mirrorEl = null\n    }\n\n    this.sourceEl = null\n  }\n\n  updateElPosition() {\n    if (this.sourceEl && this.isVisible) {\n      applyStyle(this.getMirrorEl(), {\n        left: this.sourceElRect!.left + this.deltaX!,\n        top: this.sourceElRect!.top + this.deltaY!,\n      })\n    }\n  }\n\n  getMirrorEl(): HTMLElement {\n    let sourceElRect = this.sourceElRect!\n    let mirrorEl = this.mirrorEl\n\n    if (!mirrorEl) {\n      mirrorEl = this.mirrorEl = this.sourceEl!.cloneNode(true) as HTMLElement // cloneChildren=true\n\n      // we don't want long taps or any mouse interaction causing selection/menus.\n      // would use preventSelection(), but that prevents selectstart, causing problems.\n      mirrorEl.classList.add('fc-unselectable')\n\n      mirrorEl.classList.add('fc-event-dragging')\n\n      applyStyle(mirrorEl, {\n        position: 'fixed',\n        zIndex: this.zIndex,\n        visibility: '', // in case original element was hidden by the drag effect\n        boxSizing: 'border-box', // for easy width/height\n        width: sourceElRect.right - sourceElRect.left, // explicit height in case there was a 'right' value\n        height: sourceElRect.bottom - sourceElRect.top, // explicit width in case there was a 'bottom' value\n        right: 'auto', // erase and set width instead\n        bottom: 'auto', // erase and set height instead\n        margin: 0,\n      })\n\n      this.parentNode.appendChild(mirrorEl)\n    }\n\n    return mirrorEl\n  }\n}\n", "import { Rect, ScrollController } from '@fullcalendar/common'\n\n/*\nIs a cache for a given element's scroll information (all the info that ScrollController stores)\nin addition the \"client rectangle\" of the element.. the area within the scrollbars.\n\nThe cache can be in one of two modes:\n- doesListening:false - ignores when the container is scrolled by someone else\n- doesListening:true - watch for scrolling and update the cache\n*/\nexport abstract class ScrollGeomCache extends ScrollController {\n  clientRect: Rect\n  origScrollTop: number\n  origScrollLeft: number\n\n  protected scrollController: ScrollController\n  protected doesListening: boolean\n  protected scrollTop: number\n  protected scrollLeft: number\n  protected scrollWidth: number\n  protected scrollHeight: number\n  protected clientWidth: number\n  protected clientHeight: number\n\n  constructor(scrollController: ScrollController, doesListening: boolean) {\n    super()\n    this.scrollController = scrollController\n    this.doesListening = doesListening\n    this.scrollTop = this.origScrollTop = scrollController.getScrollTop()\n    this.scrollLeft = this.origScrollLeft = scrollController.getScrollLeft()\n    this.scrollWidth = scrollController.getScrollWidth()\n    this.scrollHeight = scrollController.getScrollHeight()\n    this.clientWidth = scrollController.getClientWidth()\n    this.clientHeight = scrollController.getClientHeight()\n    this.clientRect = this.computeClientRect() // do last in case it needs cached values\n\n    if (this.doesListening) {\n      this.getEventTarget().addEventListener('scroll', this.handleScroll)\n    }\n  }\n\n  abstract getEventTarget(): EventTarget\n  abstract computeClientRect(): Rect\n\n  destroy() {\n    if (this.doesListening) {\n      this.getEventTarget().removeEventListener('scroll', this.handleScroll)\n    }\n  }\n\n  handleScroll = () => {\n    this.scrollTop = this.scrollController.getScrollTop()\n    this.scrollLeft = this.scrollController.getScrollLeft()\n    this.handleScrollChange()\n  }\n\n  getScrollTop() {\n    return this.scrollTop\n  }\n\n  getScrollLeft() {\n    return this.scrollLeft\n  }\n\n  setScrollTop(top: number) {\n    this.scrollController.setScrollTop(top)\n\n    if (!this.doesListening) {\n      // we are not relying on the element to normalize out-of-bounds scroll values\n      // so we need to sanitize ourselves\n      this.scrollTop = Math.max(Math.min(top, this.getMaxScrollTop()), 0)\n\n      this.handleScrollChange()\n    }\n  }\n\n  setScrollLeft(top: number) {\n    this.scrollController.setScrollLeft(top)\n\n    if (!this.doesListening) {\n      // we are not relying on the element to normalize out-of-bounds scroll values\n      // so we need to sanitize ourselves\n      this.scrollLeft = Math.max(Math.min(top, this.getMaxScrollLeft()), 0)\n\n      this.handleScrollChange()\n    }\n  }\n\n  getClientWidth() {\n    return this.clientWidth\n  }\n\n  getClientHeight() {\n    return this.clientHeight\n  }\n\n  getScrollWidth() {\n    return this.scrollWidth\n  }\n\n  getScrollHeight() {\n    return this.scrollHeight\n  }\n\n  handleScrollChange() {\n  }\n}\n", "import { computeInnerRect, ElementScrollController } from '@fullcalendar/common'\nimport { ScrollGeomCache } from './ScrollGeomCache'\n\nexport class ElementScrollGeomCache extends ScrollGeomCache {\n  constructor(el: HTMLElement, doesListening: boolean) {\n    super(new ElementScrollController(el), doesListening)\n  }\n\n  getEventTarget(): EventTarget {\n    return (this.scrollController as ElementScrollController).el\n  }\n\n  computeClientRect() {\n    return computeInnerRect((this.scrollController as ElementScrollController).el)\n  }\n}\n", "import { Rect, WindowScrollController } from '@fullcalendar/common'\nimport { ScrollGeomCache } from './ScrollGeomCache'\n\nexport class WindowScrollGeomCache extends ScrollGeomCache {\n  constructor(doesListening: boolean) {\n    super(new WindowScrollController(), doesListening)\n  }\n\n  getEventTarget(): EventTarget {\n    return window\n  }\n\n  computeClientRect(): Rect {\n    return {\n      left: this.scrollLeft,\n      right: this.scrollLeft + this.clientWidth,\n      top: this.scrollTop,\n      bottom: this.scrollTop + this.clientHeight,\n    }\n  }\n\n  // the window is the only scroll object that changes it's rectangle relative\n  // to the document's topleft as it scrolls\n  handleScrollChange() {\n    this.clientRect = this.computeClientRect()\n  }\n}\n", "import { getElRoot } from '@fullcalendar/common'\nimport { ScrollGeomCache } from '../ScrollGeomCache'\nimport { ElementScrollGeomCache } from '../ElementScrollGeomCache'\nimport { WindowScrollGeomCache } from '../WindowScrollGeomCache'\n\ninterface Edge {\n  scrollCache: ScrollGeomCache\n  name: 'top' | 'left' | 'right' | 'bottom'\n  distance: number // how many pixels the current pointer is from the edge\n}\n\n// If available we are using native \"performance\" API instead of \"Date\"\n// Read more about it on MDN:\n// https://developer.mozilla.org/en-US/docs/Web/API/Performance\nconst getTime = typeof performance === 'function' ? (performance as any).now : Date.now\n\n/*\nFor a pointer interaction, automatically scrolls certain scroll containers when the pointer\napproaches the edge.\n\nThe caller must call start + handleMove + stop.\n*/\nexport class AutoScroller {\n  // options that can be set by caller\n  isEnabled: boolean = true\n  scrollQuery: (Window | string)[] = [window, '.fc-scroller']\n  edgeThreshold: number = 50 // pixels\n  maxVelocity: number = 300 // pixels per second\n\n  // internal state\n  pointerScreenX: number | null = null\n  pointerScreenY: number | null = null\n  isAnimating: boolean = false\n  scrollCaches: ScrollGeomCache[] | null = null\n  msSinceRequest?: number\n\n  // protect against the initial pointerdown being too close to an edge and starting the scroll\n  everMovedUp: boolean = false\n  everMovedDown: boolean = false\n  everMovedLeft: boolean = false\n  everMovedRight: boolean = false\n\n  start(pageX: number, pageY: number, scrollStartEl: HTMLElement) {\n    if (this.isEnabled) {\n      this.scrollCaches = this.buildCaches(scrollStartEl)\n      this.pointerScreenX = null\n      this.pointerScreenY = null\n      this.everMovedUp = false\n      this.everMovedDown = false\n      this.everMovedLeft = false\n      this.everMovedRight = false\n      this.handleMove(pageX, pageY)\n    }\n  }\n\n  handleMove(pageX: number, pageY: number) {\n    if (this.isEnabled) {\n      let pointerScreenX = pageX - window.pageXOffset\n      let pointerScreenY = pageY - window.pageYOffset\n\n      let yDelta = this.pointerScreenY === null ? 0 : pointerScreenY - this.pointerScreenY\n      let xDelta = this.pointerScreenX === null ? 0 : pointerScreenX - this.pointerScreenX\n\n      if (yDelta < 0) {\n        this.everMovedUp = true\n      } else if (yDelta > 0) {\n        this.everMovedDown = true\n      }\n\n      if (xDelta < 0) {\n        this.everMovedLeft = true\n      } else if (xDelta > 0) {\n        this.everMovedRight = true\n      }\n\n      this.pointerScreenX = pointerScreenX\n      this.pointerScreenY = pointerScreenY\n\n      if (!this.isAnimating) {\n        this.isAnimating = true\n        this.requestAnimation(getTime())\n      }\n    }\n  }\n\n  stop() {\n    if (this.isEnabled) {\n      this.isAnimating = false // will stop animation\n\n      for (let scrollCache of this.scrollCaches!) {\n        scrollCache.destroy()\n      }\n\n      this.scrollCaches = null\n    }\n  }\n\n  requestAnimation(now: number) {\n    this.msSinceRequest = now\n    requestAnimationFrame(this.animate)\n  }\n\n  private animate = () => {\n    if (this.isAnimating) { // wasn't cancelled between animation calls\n      let edge = this.computeBestEdge(\n        this.pointerScreenX! + window.pageXOffset,\n        this.pointerScreenY! + window.pageYOffset,\n      )\n\n      if (edge) {\n        let now = getTime()\n        this.handleSide(edge, (now - this.msSinceRequest!) / 1000)\n        this.requestAnimation(now)\n      } else {\n        this.isAnimating = false // will stop animation\n      }\n    }\n  }\n\n  private handleSide(edge: Edge, seconds: number) {\n    let { scrollCache } = edge\n    let { edgeThreshold } = this\n    let invDistance = edgeThreshold - edge.distance\n    let velocity = // the closer to the edge, the faster we scroll\n      ((invDistance * invDistance) / (edgeThreshold * edgeThreshold)) * // quadratic\n      this.maxVelocity * seconds\n    let sign = 1\n\n    switch (edge.name) {\n      case 'left':\n        sign = -1\n        // falls through\n      case 'right':\n        scrollCache.setScrollLeft(scrollCache.getScrollLeft() + velocity * sign)\n        break\n\n      case 'top':\n        sign = -1\n        // falls through\n      case 'bottom':\n        scrollCache.setScrollTop(scrollCache.getScrollTop() + velocity * sign)\n        break\n    }\n  }\n\n  // left/top are relative to document topleft\n  private computeBestEdge(left: number, top: number): Edge | null {\n    let { edgeThreshold } = this\n    let bestSide: Edge | null = null\n\n    for (let scrollCache of this.scrollCaches!) {\n      let rect = scrollCache.clientRect\n      let leftDist = left - rect.left\n      let rightDist = rect.right - left\n      let topDist = top - rect.top\n      let bottomDist = rect.bottom - top\n\n      // completely within the rect?\n      if (leftDist >= 0 && rightDist >= 0 && topDist >= 0 && bottomDist >= 0) {\n        if (\n          topDist <= edgeThreshold && this.everMovedUp && scrollCache.canScrollUp() &&\n          (!bestSide || bestSide.distance > topDist)\n        ) {\n          bestSide = { scrollCache, name: 'top', distance: topDist }\n        }\n\n        if (\n          bottomDist <= edgeThreshold && this.everMovedDown && scrollCache.canScrollDown() &&\n          (!bestSide || bestSide.distance > bottomDist)\n        ) {\n          bestSide = { scrollCache, name: 'bottom', distance: bottomDist }\n        }\n\n        if (\n          leftDist <= edgeThreshold && this.everMovedLeft && scrollCache.canScrollLeft() &&\n          (!bestSide || bestSide.distance > leftDist)\n        ) {\n          bestSide = { scrollCache, name: 'left', distance: leftDist }\n        }\n\n        if (\n          rightDist <= edgeThreshold && this.everMovedRight && scrollCache.canScrollRight() &&\n          (!bestSide || bestSide.distance > rightDist)\n        ) {\n          bestSide = { scrollCache, name: 'right', distance: rightDist }\n        }\n      }\n    }\n\n    return bestSide\n  }\n\n  private buildCaches(scrollStartEl: HTMLElement) {\n    return this.queryScrollEls(scrollStartEl).map((el) => {\n      if (el === window) {\n        return new WindowScrollGeomCache(false) // false = don't listen to user-generated scrolls\n      }\n      return new ElementScrollGeomCache(el, false) // false = don't listen to user-generated scrolls\n    })\n  }\n\n  private queryScrollEls(scrollStartEl: HTMLElement) {\n    let els = []\n\n    for (let query of this.scrollQuery) {\n      if (typeof query === 'object') {\n        els.push(query)\n      } else {\n        els.push(...Array.prototype.slice.call(\n          getElRoot(scrollStartEl).querySelectorAll(query),\n        ))\n      }\n    }\n\n    return els\n  }\n}\n", "import {\n  PointerDragEvent,\n  preventSelection,\n  allowSelection,\n  preventContextMenu,\n  allowContextMenu,\n  ElementDragging,\n} from '@fullcalendar/common'\nimport { PointerDragging } from './PointerDragging'\nimport { ElementMirror } from './ElementMirror'\nimport { AutoScroller } from './AutoScroller'\n\n/*\nMonitors dragging on an element. Has a number of high-level features:\n- minimum distance required before dragging\n- minimum wait time (\"delay\") before dragging\n- a mirror element that follows the pointer\n*/\nexport class FeaturefulElementDragging extends ElementDragging {\n  pointer: PointerDragging\n  mirror: ElementMirror\n  autoScroller: AutoScroller\n\n  // options that can be directly set by caller\n  // the caller can also set the PointerDragging's options as well\n  delay: number | null = null\n  minDistance: number = 0\n  touchScrollAllowed: boolean = true // prevents drag from starting and blocks scrolling during drag\n\n  mirrorNeedsRevert: boolean = false\n  isInteracting: boolean = false // is the user validly moving the pointer? lasts until pointerup\n  isDragging: boolean = false // is it INTENTFULLY dragging? lasts until after revert animation\n  isDelayEnded: boolean = false\n  isDistanceSurpassed: boolean = false\n  delayTimeoutId: number | null = null\n\n  constructor(private containerEl: HTMLElement, selector?: string) {\n    super(containerEl)\n\n    let pointer = this.pointer = new PointerDragging(containerEl)\n    pointer.emitter.on('pointerdown', this.onPointerDown)\n    pointer.emitter.on('pointermove', this.onPointerMove)\n    pointer.emitter.on('pointerup', this.onPointerUp)\n\n    if (selector) {\n      pointer.selector = selector\n    }\n\n    this.mirror = new ElementMirror()\n    this.autoScroller = new AutoScroller()\n  }\n\n  destroy() {\n    this.pointer.destroy()\n\n    // HACK: simulate a pointer-up to end the current drag\n    // TODO: fire 'dragend' directly and stop interaction. discourage use of pointerup event (b/c might not fire)\n    this.onPointerUp({} as any)\n  }\n\n  onPointerDown = (ev: PointerDragEvent) => {\n    if (!this.isDragging) { // so new drag doesn't happen while revert animation is going\n      this.isInteracting = true\n      this.isDelayEnded = false\n      this.isDistanceSurpassed = false\n\n      preventSelection(document.body)\n      preventContextMenu(document.body)\n\n      // prevent links from being visited if there's an eventual drag.\n      // also prevents selection in older browsers (maybe?).\n      // not necessary for touch, besides, browser would complain about passiveness.\n      if (!ev.isTouch) {\n        ev.origEvent.preventDefault()\n      }\n\n      this.emitter.trigger('pointerdown', ev)\n\n      if (\n        this.isInteracting && // not destroyed via pointerdown handler\n        !this.pointer.shouldIgnoreMove\n      ) {\n        // actions related to initiating dragstart+dragmove+dragend...\n\n        this.mirror.setIsVisible(false) // reset. caller must set-visible\n        this.mirror.start(ev.subjectEl as HTMLElement, ev.pageX, ev.pageY) // must happen on first pointer down\n\n        this.startDelay(ev)\n\n        if (!this.minDistance) {\n          this.handleDistanceSurpassed(ev)\n        }\n      }\n    }\n  }\n\n  onPointerMove = (ev: PointerDragEvent) => {\n    if (this.isInteracting) {\n      this.emitter.trigger('pointermove', ev)\n\n      if (!this.isDistanceSurpassed) {\n        let minDistance = this.minDistance\n        let distanceSq // current distance from the origin, squared\n        let { deltaX, deltaY } = ev\n\n        distanceSq = deltaX * deltaX + deltaY * deltaY\n        if (distanceSq >= minDistance * minDistance) { // use pythagorean theorem\n          this.handleDistanceSurpassed(ev)\n        }\n      }\n\n      if (this.isDragging) {\n        // a real pointer move? (not one simulated by scrolling)\n        if (ev.origEvent.type !== 'scroll') {\n          this.mirror.handleMove(ev.pageX, ev.pageY)\n          this.autoScroller.handleMove(ev.pageX, ev.pageY)\n        }\n\n        this.emitter.trigger('dragmove', ev)\n      }\n    }\n  }\n\n  onPointerUp = (ev: PointerDragEvent) => {\n    if (this.isInteracting) {\n      this.isInteracting = false\n\n      allowSelection(document.body)\n      allowContextMenu(document.body)\n\n      this.emitter.trigger('pointerup', ev) // can potentially set mirrorNeedsRevert\n\n      if (this.isDragging) {\n        this.autoScroller.stop()\n        this.tryStopDrag(ev) // which will stop the mirror\n      }\n\n      if (this.delayTimeoutId) {\n        clearTimeout(this.delayTimeoutId)\n        this.delayTimeoutId = null\n      }\n    }\n  }\n\n  startDelay(ev: PointerDragEvent) {\n    if (typeof this.delay === 'number') {\n      this.delayTimeoutId = setTimeout(() => {\n        this.delayTimeoutId = null\n        this.handleDelayEnd(ev)\n      }, this.delay) as any // not assignable to number!\n    } else {\n      this.handleDelayEnd(ev)\n    }\n  }\n\n  handleDelayEnd(ev: PointerDragEvent) {\n    this.isDelayEnded = true\n    this.tryStartDrag(ev)\n  }\n\n  handleDistanceSurpassed(ev: PointerDragEvent) {\n    this.isDistanceSurpassed = true\n    this.tryStartDrag(ev)\n  }\n\n  tryStartDrag(ev: PointerDragEvent) {\n    if (this.isDelayEnded && this.isDistanceSurpassed) {\n      if (!this.pointer.wasTouchScroll || this.touchScrollAllowed) {\n        this.isDragging = true\n        this.mirrorNeedsRevert = false\n\n        this.autoScroller.start(ev.pageX, ev.pageY, this.containerEl)\n        this.emitter.trigger('dragstart', ev)\n\n        if (this.touchScrollAllowed === false) {\n          this.pointer.cancelTouchScroll()\n        }\n      }\n    }\n  }\n\n  tryStopDrag(ev: PointerDragEvent) {\n    // .stop() is ALWAYS asynchronous, which we NEED because we want all pointerup events\n    // that come from the document to fire beforehand. much more convenient this way.\n    this.mirror.stop(\n      this.mirrorNeedsRevert,\n      this.stopDrag.bind(this, ev), // bound with args\n    )\n  }\n\n  stopDrag(ev: PointerDragEvent) {\n    this.isDragging = false\n    this.emitter.trigger('dragend', ev)\n  }\n\n  // fill in the implementations...\n\n  setIgnoreMove(bool: boolean) {\n    this.pointer.shouldIgnoreMove = bool\n  }\n\n  setMirrorIsVisible(bool: boolean) {\n    this.mirror.setIsVisible(bool)\n  }\n\n  setMirrorNeedsRevert(bool: boolean) {\n    this.mirrorNeedsRevert = bool\n  }\n\n  setAutoScrollEnabled(bool: boolean) {\n    this.autoScroller.isEnabled = bool\n  }\n}\n", "import {\n  getClippingParents, computeRect,\n  pointInsideRect, Rect,\n} from '@fullcalendar/common'\nimport { ElementScrollGeomCache } from './ElementScrollGeomCache'\n\n/*\nWhen this class is instantiated, it records the offset of an element (relative to the document topleft),\nand continues to monitor scrolling, updating the cached coordinates if it needs to.\nDoes not access the DOM after instantiation, so highly performant.\n\nAlso keeps track of all scrolling/overflow:hidden containers that are parents of the given element\nand an determine if a given point is inside the combined clipping rectangle.\n*/\nexport class OffsetTracker { // ElementOffsetTracker\n  scrollCaches: ElementScrollGeomCache[]\n  origRect: Rect\n\n  constructor(el: HTMLElement) {\n    this.origRect = computeRect(el)\n\n    // will work fine for divs that have overflow:hidden\n    this.scrollCaches = getClippingParents(el).map(\n      (scrollEl) => new ElementScrollGeomCache(scrollEl, true), // listen=true\n    )\n  }\n\n  destroy() {\n    for (let scrollCache of this.scrollCaches) {\n      scrollCache.destroy()\n    }\n  }\n\n  computeLeft() {\n    let left = this.origRect.left\n\n    for (let scrollCache of this.scrollCaches) {\n      left += scrollCache.origScrollLeft - scrollCache.getScrollLeft()\n    }\n\n    return left\n  }\n\n  computeTop() {\n    let top = this.origRect.top\n\n    for (let scrollCache of this.scrollCaches) {\n      top += scrollCache.origScrollTop - scrollCache.getScrollTop()\n    }\n\n    return top\n  }\n\n  isWithinClipping(pageX: number, pageY: number): boolean {\n    let point = { left: pageX, top: pageY }\n\n    for (let scrollCache of this.scrollCaches) {\n      if (\n        !isIgnoredClipping(scrollCache.getEventTarget()) &&\n        !pointInsideRect(point, scrollCache.clientRect)\n      ) {\n        return false\n      }\n    }\n\n    return true\n  }\n}\n\n// certain clipping containers should never constrain interactions, like <html> and <body>\n// https://github.com/fullcalendar/fullcalendar/issues/3615\nfunction isIgnoredClipping(node: EventTarget) {\n  let tagName = (node as HTMLElement).tagName\n\n  return tagName === 'HTML' || tagName === 'BODY'\n}\n", "import {\n  Emitter, PointerDragEvent,\n  isDateSpansEqual,\n  computeRect,\n  constrainPoint, intersectRects, getRectCenter, diffPoints, Point,\n  rangeContainsRange,\n  Hit,\n  InteractionSettingsStore,\n  mapHash,\n  ElementDragging,\n} from '@fullcalendar/common'\nimport { OffsetTracker } from '../OffsetTracker'\n\n/*\nTracks movement over multiple droppable areas (aka \"hits\")\nthat exist in one or more DateComponents.\nRelies on an existing draggable.\n\nemits:\n- pointerdown\n- dragstart\n- hitchange - fires initially, even if not over a hit\n- pointerup\n- (hitchange - again, to null, if ended over a hit)\n- dragend\n*/\nexport class HitDragging {\n  droppableStore: InteractionSettingsStore\n  dragging: ElementDragging\n  emitter: Emitter<any>\n\n  // options that can be set by caller\n  useSubjectCenter: boolean = false\n  requireInitial: boolean = true // if doesn't start out on a hit, won't emit any events\n\n  // internal state\n  offsetTrackers: { [componentUid: string]: OffsetTracker }\n  initialHit: Hit | null = null\n  movingHit: Hit | null = null\n  finalHit: Hit | null = null // won't ever be populated if shouldIgnoreMove\n  coordAdjust?: Point\n\n  constructor(dragging: ElementDragging, droppableStore: InteractionSettingsStore) {\n    this.droppableStore = droppableStore\n\n    dragging.emitter.on('pointerdown', this.handlePointerDown)\n    dragging.emitter.on('dragstart', this.handleDragStart)\n    dragging.emitter.on('dragmove', this.handleDragMove)\n    dragging.emitter.on('pointerup', this.handlePointerUp)\n    dragging.emitter.on('dragend', this.handleDragEnd)\n\n    this.dragging = dragging\n    this.emitter = new Emitter()\n  }\n\n  handlePointerDown = (ev: PointerDragEvent) => {\n    let { dragging } = this\n\n    this.initialHit = null\n    this.movingHit = null\n    this.finalHit = null\n\n    this.prepareHits()\n    this.processFirstCoord(ev)\n\n    if (this.initialHit || !this.requireInitial) {\n      dragging.setIgnoreMove(false)\n\n      // TODO: fire this before computing processFirstCoord, so listeners can cancel. this gets fired by almost every handler :(\n      this.emitter.trigger('pointerdown', ev)\n    } else {\n      dragging.setIgnoreMove(true)\n    }\n  }\n\n  // sets initialHit\n  // sets coordAdjust\n  processFirstCoord(ev: PointerDragEvent) {\n    let origPoint = { left: ev.pageX, top: ev.pageY }\n    let adjustedPoint = origPoint\n    let subjectEl = ev.subjectEl\n    let subjectRect\n\n    if (subjectEl instanceof HTMLElement) { // i.e. not a Document/ShadowRoot\n      subjectRect = computeRect(subjectEl)\n      adjustedPoint = constrainPoint(adjustedPoint, subjectRect)\n    }\n\n    let initialHit = this.initialHit = this.queryHitForOffset(adjustedPoint.left, adjustedPoint.top)\n    if (initialHit) {\n      if (this.useSubjectCenter && subjectRect) {\n        let slicedSubjectRect = intersectRects(subjectRect, initialHit.rect)\n        if (slicedSubjectRect) {\n          adjustedPoint = getRectCenter(slicedSubjectRect)\n        }\n      }\n\n      this.coordAdjust = diffPoints(adjustedPoint, origPoint)\n    } else {\n      this.coordAdjust = { left: 0, top: 0 }\n    }\n  }\n\n  handleDragStart = (ev: PointerDragEvent) => {\n    this.emitter.trigger('dragstart', ev)\n    this.handleMove(ev, true) // force = fire even if initially null\n  }\n\n  handleDragMove = (ev: PointerDragEvent) => {\n    this.emitter.trigger('dragmove', ev)\n    this.handleMove(ev)\n  }\n\n  handlePointerUp = (ev: PointerDragEvent) => {\n    this.releaseHits()\n    this.emitter.trigger('pointerup', ev)\n  }\n\n  handleDragEnd = (ev: PointerDragEvent) => {\n    if (this.movingHit) {\n      this.emitter.trigger('hitupdate', null, true, ev)\n    }\n\n    this.finalHit = this.movingHit\n    this.movingHit = null\n    this.emitter.trigger('dragend', ev)\n  }\n\n  handleMove(ev: PointerDragEvent, forceHandle?: boolean) {\n    let hit = this.queryHitForOffset(\n      ev.pageX + this.coordAdjust!.left,\n      ev.pageY + this.coordAdjust!.top,\n    )\n\n    if (forceHandle || !isHitsEqual(this.movingHit, hit)) {\n      this.movingHit = hit\n      this.emitter.trigger('hitupdate', hit, false, ev)\n    }\n  }\n\n  prepareHits() {\n    this.offsetTrackers = mapHash(this.droppableStore, (interactionSettings) => {\n      interactionSettings.component.prepareHits()\n      return new OffsetTracker(interactionSettings.el)\n    })\n  }\n\n  releaseHits() {\n    let { offsetTrackers } = this\n\n    for (let id in offsetTrackers) {\n      offsetTrackers[id].destroy()\n    }\n\n    this.offsetTrackers = {}\n  }\n\n  queryHitForOffset(offsetLeft: number, offsetTop: number): Hit | null {\n    let { droppableStore, offsetTrackers } = this\n    let bestHit: Hit | null = null\n\n    for (let id in droppableStore) {\n      let component = droppableStore[id].component\n      let offsetTracker = offsetTrackers[id]\n\n      if (\n        offsetTracker && // wasn't destroyed mid-drag\n        offsetTracker.isWithinClipping(offsetLeft, offsetTop)\n      ) {\n        let originLeft = offsetTracker.computeLeft()\n        let originTop = offsetTracker.computeTop()\n        let positionLeft = offsetLeft - originLeft\n        let positionTop = offsetTop - originTop\n        let { origRect } = offsetTracker\n        let width = origRect.right - origRect.left\n        let height = origRect.bottom - origRect.top\n\n        if (\n          // must be within the element's bounds\n          positionLeft >= 0 && positionLeft < width &&\n          positionTop >= 0 && positionTop < height\n        ) {\n          let hit = component.queryHit(positionLeft, positionTop, width, height)\n          if (\n            hit && (\n              // make sure the hit is within activeRange, meaning it's not a dead cell\n              rangeContainsRange(hit.dateProfile.activeRange, hit.dateSpan.range)\n            ) &&\n            (!bestHit || hit.layer > bestHit.layer)\n          ) {\n            hit.componentId = id\n            hit.context = component.context\n\n            // TODO: better way to re-orient rectangle\n            hit.rect.left += originLeft\n            hit.rect.right += originLeft\n            hit.rect.top += originTop\n            hit.rect.bottom += originTop\n\n            bestHit = hit\n          }\n        }\n      }\n    }\n\n    return bestHit\n  }\n}\n\nexport function isHitsEqual(hit0: Hit | null, hit1: Hit | null): boolean {\n  if (!hit0 && !hit1) {\n    return true\n  }\n\n  if (Boolean(hit0) !== Boolean(hit1)) {\n    return false\n  }\n\n  return isDateSpansEqual(hit0!.dateSpan, hit1!.dateSpan)\n}\n", "import { DateSpan, CalendarContext, DatePoint<PERSON><PERSON>, DateEnv, ViewApi, EventApi } from '@fullcalendar/common'\nimport { __assign } from 'tslib'\n\nexport interface DropArg extends DatePointApi {\n  draggedEl: HTMLElement\n  jsEvent: MouseEvent\n  view: ViewApi\n}\n\nexport type EventReceiveArg = EventReceiveLeaveArg\nexport type EventLeaveArg = EventReceiveLeaveArg\nexport interface EventReceiveLeaveArg { // will this become public?\n  draggedEl: HTMLElement\n  event: EventApi\n  relatedEvents: EventApi[]\n  revert: () => void\n  view: ViewApi\n}\n\nexport function buildDatePointApiWithContext(dateSpan: DateSpan, context: CalendarContext) {\n  let props = {} as DatePointApi\n\n  for (let transform of context.pluginHooks.datePointTransforms) {\n    __assign(props, transform(dateSpan, context))\n  }\n\n  __assign(props, buildDatePointApi(dateSpan, context.dateEnv))\n\n  return props\n}\n\nexport function buildDatePointApi(span: DateSpan, dateEnv: DateEnv): DatePoint<PERSON>pi {\n  return {\n    date: dateEnv.toDate(span.range.start),\n    dateStr: dateEnv.formatIso(span.range.start, { omitTime: span.allDay }),\n    allDay: span.allDay,\n  }\n}\n", "import {\n  PointerDragEvent, Interaction, InteractionSettings, interactionSettingsToStore,\n  DatePointApi,\n  ViewApi,\n} from '@fullcalendar/common'\nimport { FeaturefulElementDragging } from '../dnd/FeaturefulElementDragging'\nimport { HitDragging, isHitsEqual } from './HitDragging'\nimport { buildDatePointApiWithContext } from '../utils'\n\nexport interface DateClickArg extends DatePointApi {\n  dayEl: HTMLElement\n  jsEvent: MouseEvent\n  view: ViewApi\n}\n\n/*\nMonitors when the user clicks on a specific date/time of a component.\nA pointerdown+pointerup on the same \"hit\" constitutes a click.\n*/\nexport class DateClicking extends Interaction {\n  dragging: FeaturefulElementDragging\n  hitDragging: HitDragging\n\n  constructor(settings: InteractionSettings) {\n    super(settings)\n\n    // we DO want to watch pointer moves because otherwise finalHit won't get populated\n    this.dragging = new FeaturefulElementDragging(settings.el)\n    this.dragging.autoScroller.isEnabled = false\n\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings))\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown)\n    hitDragging.emitter.on('dragend', this.handleDragEnd)\n  }\n\n  destroy() {\n    this.dragging.destroy()\n  }\n\n  handlePointerDown = (pev: PointerDragEvent) => {\n    let { dragging } = this\n    let downEl = pev.origEvent.target as HTMLElement\n\n    // do this in pointerdown (not dragend) because DOM might be mutated by the time dragend is fired\n    dragging.setIgnoreMove(\n      !this.component.isValidDateDownEl(downEl),\n    )\n  }\n\n  // won't even fire if moving was ignored\n  handleDragEnd = (ev: PointerDragEvent) => {\n    let { component } = this\n    let { pointer } = this.dragging\n\n    if (!pointer.wasTouchScroll) {\n      let { initialHit, finalHit } = this.hitDragging\n\n      if (initialHit && finalHit && isHitsEqual(initialHit, finalHit)) {\n        let { context } = component\n        let arg: DateClickArg = {\n          ...buildDatePointApiWithContext(initialHit.dateSpan, context),\n          dayEl: initialHit.dayEl,\n          jsEvent: ev.origEvent as MouseEvent,\n          view: context.viewApi || context.calendarApi.view,\n        }\n\n        context.emitter.trigger('dateClick', arg)\n      }\n    }\n  }\n}\n", "import {\n  compareNumbers, enableCursor, disableCursor, DateComponent, Hit,\n  DateSpan, PointerDragEvent, dateSelectionJoinTransformer,\n  Interaction, InteractionSettings, interactionSettingsToStore,\n  triggerDateSelect, isDateSelectionValid,\n} from '@fullcalendar/common'\nimport { __assign } from 'tslib'\nimport { HitDragging } from './HitDragging'\nimport { FeaturefulElementDragging } from '../dnd/FeaturefulElementDragging'\n\n/*\nTracks when the user selects a portion of time of a component,\nconstituted by a drag over date cells, with a possible delay at the beginning of the drag.\n*/\nexport class DateSelecting extends Interaction {\n  dragging: FeaturefulElementDragging\n  hitDragging: HitDragging\n  dragSelection: DateSpan | null = null\n\n  constructor(settings: InteractionSettings) {\n    super(settings)\n    let { component } = settings\n    let { options } = component.context\n\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el)\n    dragging.touchScrollAllowed = false\n    dragging.minDistance = options.selectMinDistance || 0\n    dragging.autoScroller.isEnabled = options.dragScroll\n\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings))\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown)\n    hitDragging.emitter.on('dragstart', this.handleDragStart)\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate)\n    hitDragging.emitter.on('pointerup', this.handlePointerUp)\n  }\n\n  destroy() {\n    this.dragging.destroy()\n  }\n\n  handlePointerDown = (ev: PointerDragEvent) => {\n    let { component, dragging } = this\n    let { options } = component.context\n\n    let canSelect = options.selectable &&\n      component.isValidDateDownEl(ev.origEvent.target as HTMLElement)\n\n    // don't bother to watch expensive moves if component won't do selection\n    dragging.setIgnoreMove(!canSelect)\n\n    // if touch, require user to hold down\n    dragging.delay = ev.isTouch ? getComponentTouchDelay(component) : null\n  }\n\n  handleDragStart = (ev: PointerDragEvent) => {\n    this.component.context.calendarApi.unselect(ev) // unselect previous selections\n  }\n\n  handleHitUpdate = (hit: Hit | null, isFinal: boolean) => {\n    let { context } = this.component\n    let dragSelection: DateSpan | null = null\n    let isInvalid = false\n\n    if (hit) {\n      let initialHit = this.hitDragging.initialHit!\n      let disallowed = hit.componentId === initialHit.componentId\n        && this.isHitComboAllowed\n        && !this.isHitComboAllowed(initialHit, hit)\n\n      if (!disallowed) {\n        dragSelection = joinHitsIntoSelection(\n          initialHit,\n          hit,\n          context.pluginHooks.dateSelectionTransformers,\n        )\n      }\n\n      if (!dragSelection || !isDateSelectionValid(dragSelection, hit.dateProfile, context)) {\n        isInvalid = true\n        dragSelection = null\n      }\n    }\n\n    if (dragSelection) {\n      context.dispatch({ type: 'SELECT_DATES', selection: dragSelection })\n    } else if (!isFinal) { // only unselect if moved away while dragging\n      context.dispatch({ type: 'UNSELECT_DATES' })\n    }\n\n    if (!isInvalid) {\n      enableCursor()\n    } else {\n      disableCursor()\n    }\n\n    if (!isFinal) {\n      this.dragSelection = dragSelection // only clear if moved away from all hits while dragging\n    }\n  }\n\n  handlePointerUp = (pev: PointerDragEvent) => {\n    if (this.dragSelection) {\n      // selection is already rendered, so just need to report selection\n      triggerDateSelect(this.dragSelection, pev, this.component.context)\n\n      this.dragSelection = null\n    }\n  }\n}\n\nfunction getComponentTouchDelay(component: DateComponent<any>): number {\n  let { options } = component.context\n  let delay = options.selectLongPressDelay\n\n  if (delay == null) {\n    delay = options.longPressDelay\n  }\n\n  return delay\n}\n\nfunction joinHitsIntoSelection(hit0: Hit, hit1: Hit, dateSelectionTransformers: dateSelectionJoinTransformer[]): DateSpan {\n  let dateSpan0 = hit0.dateSpan\n  let dateSpan1 = hit1.dateSpan\n  let ms = [\n    dateSpan0.range.start,\n    dateSpan0.range.end,\n    dateSpan1.range.start,\n    dateSpan1.range.end,\n  ]\n\n  ms.sort(compareNumbers)\n\n  let props = {} as DateSpan\n\n  for (let transformer of dateSelectionTransformers) {\n    let res = transformer(hit0, hit1)\n\n    if (res === false) {\n      return null\n    }\n\n    if (res) {\n      __assign(props, res)\n    }\n  }\n\n  props.range = { start: ms[0], end: ms[3] }\n  props.allDay = dateSpan0.allDay\n\n  return props\n}\n", "import {\n  DateComponent, Seg,\n  PointerDragEvent, Hit,\n  EventMutation, applyMutationToEventStore,\n  startOfDay,\n  elementClosest,\n  EventStore, getRelevantEvents, createEmptyEventStore,\n  EventInteractionState,\n  diffDates, enableCursor, disable<PERSON>ursor,\n  EventRenderRange, getElSeg,\n  EventApi,\n  eventDragMutationMassager,\n  Interaction, InteractionSettings, interactionSettingsStore,\n  EventDropTransformers,\n  CalendarContext,\n  ViewApi,\n  EventChangeArg,\n  buildEventApis,\n  EventAddArg,\n  EventRemoveArg,\n  isInteractionValid,\n  getElRoot,\n} from '@fullcalendar/common'\nimport { __assign } from 'tslib'\nimport { HitDragging, isHitsEqual } from './HitDragging'\nimport { FeaturefulElementDragging } from '../dnd/FeaturefulElementDragging'\nimport { buildDatePointApiWithContext } from '../utils'\n\nexport type EventDragStopArg = EventDragArg\nexport type EventDragStartArg = EventDragArg\n\nexport interface EventDragArg {\n  el: HTMLElement\n  event: EventApi\n  jsEvent: MouseEvent\n  view: ViewApi\n}\n\nexport class EventDragging extends Interaction { // TODO: rename to EventSelectingAndDragging\n  // TODO: test this in IE11\n  // QUESTION: why do we need it on the resizable???\n  static SELECTOR = '.fc-event-draggable, .fc-event-resizable'\n\n  dragging: FeaturefulElementDragging\n  hitDragging: HitDragging\n\n  // internal state\n  subjectEl: HTMLElement | null = null\n  subjectSeg: Seg | null = null // the seg being selected/dragged\n  isDragging: boolean = false\n  eventRange: EventRenderRange | null = null\n  relevantEvents: EventStore | null = null // the events being dragged\n  receivingContext: CalendarContext | null = null\n  validMutation: EventMutation | null = null\n  mutatedRelevantEvents: EventStore | null = null\n\n  constructor(settings: InteractionSettings) {\n    super(settings)\n    let { component } = this\n    let { options } = component.context\n\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el)\n    dragging.pointer.selector = EventDragging.SELECTOR\n    dragging.touchScrollAllowed = false\n    dragging.autoScroller.isEnabled = options.dragScroll\n\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsStore)\n    hitDragging.useSubjectCenter = settings.useEventCenter\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown)\n    hitDragging.emitter.on('dragstart', this.handleDragStart)\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate)\n    hitDragging.emitter.on('pointerup', this.handlePointerUp)\n    hitDragging.emitter.on('dragend', this.handleDragEnd)\n  }\n\n  destroy() {\n    this.dragging.destroy()\n  }\n\n  handlePointerDown = (ev: PointerDragEvent) => {\n    let origTarget = ev.origEvent.target as HTMLElement\n    let { component, dragging } = this\n    let { mirror } = dragging\n    let { options } = component.context\n    let initialContext = component.context\n    this.subjectEl = ev.subjectEl as HTMLElement\n    let subjectSeg = this.subjectSeg = getElSeg(ev.subjectEl as HTMLElement)!\n    let eventRange = this.eventRange = subjectSeg.eventRange!\n    let eventInstanceId = eventRange.instance!.instanceId\n\n    this.relevantEvents = getRelevantEvents(\n      initialContext.getCurrentData().eventStore,\n      eventInstanceId,\n    )\n\n    dragging.minDistance = ev.isTouch ? 0 : options.eventDragMinDistance\n    dragging.delay =\n      // only do a touch delay if touch and this event hasn't been selected yet\n      (ev.isTouch && eventInstanceId !== component.props.eventSelection) ?\n        getComponentTouchDelay(component) :\n        null\n\n    if (options.fixedMirrorParent) {\n      mirror.parentNode = options.fixedMirrorParent\n    } else {\n      mirror.parentNode = elementClosest(origTarget, '.fc')\n    }\n\n    mirror.revertDuration = options.dragRevertDuration\n\n    let isValid =\n      component.isValidSegDownEl(origTarget) &&\n      !elementClosest(origTarget, '.fc-event-resizer') // NOT on a resizer\n\n    dragging.setIgnoreMove(!isValid)\n\n    // disable dragging for elements that are resizable (ie, selectable)\n    // but are not draggable\n    this.isDragging = isValid &&\n      (ev.subjectEl as HTMLElement).classList.contains('fc-event-draggable')\n  }\n\n  handleDragStart = (ev: PointerDragEvent) => {\n    let initialContext = this.component.context\n    let eventRange = this.eventRange!\n    let eventInstanceId = eventRange.instance.instanceId\n\n    if (ev.isTouch) {\n      // need to select a different event?\n      if (eventInstanceId !== this.component.props.eventSelection) {\n        initialContext.dispatch({ type: 'SELECT_EVENT', eventInstanceId })\n      }\n    } else {\n      // if now using mouse, but was previous touch interaction, clear selected event\n      initialContext.dispatch({ type: 'UNSELECT_EVENT' })\n    }\n\n    if (this.isDragging) {\n      initialContext.calendarApi.unselect(ev) // unselect *date* selection\n      initialContext.emitter.trigger('eventDragStart', {\n        el: this.subjectEl,\n        event: new EventApi(initialContext, eventRange.def, eventRange.instance),\n        jsEvent: ev.origEvent as MouseEvent, // Is this always a mouse event? See #4655\n        view: initialContext.viewApi,\n      } as EventDragStartArg)\n    }\n  }\n\n  handleHitUpdate = (hit: Hit | null, isFinal: boolean) => {\n    if (!this.isDragging) {\n      return\n    }\n\n    let relevantEvents = this.relevantEvents!\n    let initialHit = this.hitDragging.initialHit!\n    let initialContext = this.component.context\n\n    // states based on new hit\n    let receivingContext: CalendarContext | null = null\n    let mutation: EventMutation | null = null\n    let mutatedRelevantEvents: EventStore | null = null\n    let isInvalid = false\n    let interaction: EventInteractionState = {\n      affectedEvents: relevantEvents,\n      mutatedEvents: createEmptyEventStore(),\n      isEvent: true,\n    }\n\n    if (hit) {\n      receivingContext = hit.context\n      let receivingOptions = receivingContext.options\n\n      if (\n        initialContext === receivingContext ||\n        (receivingOptions.editable && receivingOptions.droppable)\n      ) {\n        mutation = computeEventMutation(initialHit, hit, receivingContext.getCurrentData().pluginHooks.eventDragMutationMassagers)\n\n        if (mutation) {\n          mutatedRelevantEvents = applyMutationToEventStore(\n            relevantEvents,\n            receivingContext.getCurrentData().eventUiBases,\n            mutation,\n            receivingContext,\n          )\n          interaction.mutatedEvents = mutatedRelevantEvents\n\n          if (!isInteractionValid(interaction, hit.dateProfile, receivingContext)) {\n            isInvalid = true\n            mutation = null\n            mutatedRelevantEvents = null\n            interaction.mutatedEvents = createEmptyEventStore()\n          }\n        }\n      } else {\n        receivingContext = null\n      }\n    }\n\n    this.displayDrag(receivingContext, interaction)\n\n    if (!isInvalid) {\n      enableCursor()\n    } else {\n      disableCursor()\n    }\n\n    if (!isFinal) {\n      if (\n        initialContext === receivingContext && // TODO: write test for this\n        isHitsEqual(initialHit, hit)\n      ) {\n        mutation = null\n      }\n\n      this.dragging.setMirrorNeedsRevert(!mutation)\n\n      // render the mirror if no already-rendered mirror\n      // TODO: wish we could somehow wait for dispatch to guarantee render\n      this.dragging.setMirrorIsVisible(\n        !hit || !getElRoot(this.subjectEl).querySelector('.fc-event-mirror'), // TODO: turn className into constant\n      )\n\n      // assign states based on new hit\n      this.receivingContext = receivingContext\n      this.validMutation = mutation\n      this.mutatedRelevantEvents = mutatedRelevantEvents\n    }\n  }\n\n  handlePointerUp = () => {\n    if (!this.isDragging) {\n      this.cleanup() // because handleDragEnd won't fire\n    }\n  }\n\n  handleDragEnd = (ev: PointerDragEvent) => {\n    if (this.isDragging) {\n      let initialContext = this.component.context\n      let initialView = initialContext.viewApi\n      let { receivingContext, validMutation } = this\n      let eventDef = this.eventRange!.def\n      let eventInstance = this.eventRange!.instance\n      let eventApi = new EventApi(initialContext, eventDef, eventInstance)\n      let relevantEvents = this.relevantEvents!\n      let mutatedRelevantEvents = this.mutatedRelevantEvents!\n      let { finalHit } = this.hitDragging\n\n      this.clearDrag() // must happen after revert animation\n\n      initialContext.emitter.trigger('eventDragStop', {\n        el: this.subjectEl,\n        event: eventApi,\n        jsEvent: ev.origEvent as MouseEvent, // Is this always a mouse event? See #4655\n        view: initialView,\n      } as EventDragStopArg)\n\n      if (validMutation) {\n        // dropped within same calendar\n        if (receivingContext === initialContext) {\n          let updatedEventApi = new EventApi(\n            initialContext,\n            mutatedRelevantEvents.defs[eventDef.defId],\n            eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null,\n          )\n\n          initialContext.dispatch({\n            type: 'MERGE_EVENTS',\n            eventStore: mutatedRelevantEvents,\n          })\n\n          let eventChangeArg: EventChangeArg = {\n            oldEvent: eventApi,\n            event: updatedEventApi,\n            relatedEvents: buildEventApis(mutatedRelevantEvents, initialContext, eventInstance),\n            revert() {\n              initialContext.dispatch({\n                type: 'MERGE_EVENTS',\n                eventStore: relevantEvents, // the pre-change data\n              })\n            },\n          }\n\n          let transformed: ReturnType<EventDropTransformers> = {}\n          for (let transformer of initialContext.getCurrentData().pluginHooks.eventDropTransformers) {\n            __assign(transformed, transformer(validMutation, initialContext))\n          }\n\n          initialContext.emitter.trigger('eventDrop', {\n            ...eventChangeArg,\n            ...transformed,\n            el: ev.subjectEl as HTMLElement,\n            delta: validMutation.datesDelta!,\n            jsEvent: ev.origEvent as MouseEvent, // bad\n            view: initialView,\n          })\n\n          initialContext.emitter.trigger('eventChange', eventChangeArg)\n\n        // dropped in different calendar\n        } else if (receivingContext) {\n          let eventRemoveArg: EventRemoveArg = {\n            event: eventApi,\n            relatedEvents: buildEventApis(relevantEvents, initialContext, eventInstance),\n            revert() {\n              initialContext.dispatch({\n                type: 'MERGE_EVENTS',\n                eventStore: relevantEvents,\n              })\n            },\n          }\n\n          initialContext.emitter.trigger('eventLeave', {\n            ...eventRemoveArg,\n            draggedEl: ev.subjectEl as HTMLElement,\n            view: initialView,\n          })\n\n          initialContext.dispatch({\n            type: 'REMOVE_EVENTS',\n            eventStore: relevantEvents,\n          })\n\n          initialContext.emitter.trigger('eventRemove', eventRemoveArg)\n\n          let addedEventDef = mutatedRelevantEvents.defs[eventDef.defId]\n          let addedEventInstance = mutatedRelevantEvents.instances[eventInstance.instanceId]\n          let addedEventApi = new EventApi(receivingContext, addedEventDef, addedEventInstance)\n\n          receivingContext.dispatch({\n            type: 'MERGE_EVENTS',\n            eventStore: mutatedRelevantEvents,\n          })\n\n          let eventAddArg: EventAddArg = {\n            event: addedEventApi,\n            relatedEvents: buildEventApis(mutatedRelevantEvents, receivingContext, addedEventInstance),\n            revert() {\n              receivingContext.dispatch({\n                type: 'REMOVE_EVENTS',\n                eventStore: mutatedRelevantEvents,\n              })\n            },\n          }\n\n          receivingContext.emitter.trigger('eventAdd', eventAddArg)\n\n          if (ev.isTouch) {\n            receivingContext.dispatch({\n              type: 'SELECT_EVENT',\n              eventInstanceId: eventInstance.instanceId,\n            })\n          }\n\n          receivingContext.emitter.trigger('drop', {\n            ...buildDatePointApiWithContext(finalHit.dateSpan, receivingContext),\n            draggedEl: ev.subjectEl as HTMLElement,\n            jsEvent: ev.origEvent as MouseEvent, // Is this always a mouse event? See #4655\n            view: finalHit.context.viewApi,\n          })\n\n          receivingContext.emitter.trigger('eventReceive', {\n            ...eventAddArg,\n            draggedEl: ev.subjectEl as HTMLElement,\n            view: finalHit.context.viewApi,\n          })\n        }\n      } else {\n        initialContext.emitter.trigger('_noEventDrop')\n      }\n    }\n\n    this.cleanup()\n  }\n\n  // render a drag state on the next receivingCalendar\n  displayDrag(nextContext: CalendarContext | null, state: EventInteractionState) {\n    let initialContext = this.component.context\n    let prevContext = this.receivingContext\n\n    // does the previous calendar need to be cleared?\n    if (prevContext && prevContext !== nextContext) {\n      // does the initial calendar need to be cleared?\n      // if so, don't clear all the way. we still need to to hide the affectedEvents\n      if (prevContext === initialContext) {\n        prevContext.dispatch({\n          type: 'SET_EVENT_DRAG',\n          state: {\n            affectedEvents: state.affectedEvents,\n            mutatedEvents: createEmptyEventStore(),\n            isEvent: true,\n          },\n        })\n\n      // completely clear the old calendar if it wasn't the initial\n      } else {\n        prevContext.dispatch({ type: 'UNSET_EVENT_DRAG' })\n      }\n    }\n\n    if (nextContext) {\n      nextContext.dispatch({ type: 'SET_EVENT_DRAG', state })\n    }\n  }\n\n  clearDrag() {\n    let initialCalendar = this.component.context\n    let { receivingContext } = this\n\n    if (receivingContext) {\n      receivingContext.dispatch({ type: 'UNSET_EVENT_DRAG' })\n    }\n\n    // the initial calendar might have an dummy drag state from displayDrag\n    if (initialCalendar !== receivingContext) {\n      initialCalendar.dispatch({ type: 'UNSET_EVENT_DRAG' })\n    }\n  }\n\n  cleanup() { // reset all internal state\n    this.subjectSeg = null\n    this.isDragging = false\n    this.eventRange = null\n    this.relevantEvents = null\n    this.receivingContext = null\n    this.validMutation = null\n    this.mutatedRelevantEvents = null\n  }\n}\n\nfunction computeEventMutation(hit0: Hit, hit1: Hit, massagers: eventDragMutationMassager[]): EventMutation {\n  let dateSpan0 = hit0.dateSpan\n  let dateSpan1 = hit1.dateSpan\n  let date0 = dateSpan0.range.start\n  let date1 = dateSpan1.range.start\n  let standardProps = {} as any\n\n  if (dateSpan0.allDay !== dateSpan1.allDay) {\n    standardProps.allDay = dateSpan1.allDay\n    standardProps.hasEnd = hit1.context.options.allDayMaintainDuration\n\n    if (dateSpan1.allDay) {\n      // means date1 is already start-of-day,\n      // but date0 needs to be converted\n      date0 = startOfDay(date0)\n    }\n  }\n\n  let delta = diffDates(\n    date0, date1,\n    hit0.context.dateEnv,\n    hit0.componentId === hit1.componentId ?\n      hit0.largeUnit :\n      null,\n  )\n\n  if (delta.milliseconds) { // has hours/minutes/seconds\n    standardProps.allDay = false\n  }\n\n  let mutation: EventMutation = {\n    datesDelta: delta,\n    standardProps,\n  }\n\n  for (let massager of massagers) {\n    massager(mutation, hit0, hit1)\n  }\n\n  return mutation\n}\n\nfunction getComponentTouchDelay(component: DateComponent<any>): number | null {\n  let { options } = component.context\n  let delay = options.eventLongPressDelay\n\n  if (delay == null) {\n    delay = options.longPressDelay\n  }\n\n  return delay\n}\n", "import {\n  Seg, Hit,\n  EventMutation, applyMutationToEventStore,\n  elementClosest,\n  PointerDragEvent,\n  EventStore, getRelevantEvents, createEmptyEventStore,\n  diffDates, enableCursor, disableCursor,\n  DateRange,\n  EventApi,\n  EventRenderRange, getElSeg,\n  createDuration,\n  EventInteractionState,\n  Interaction, InteractionSettings, interactionSettingsToStore, ViewApi, Duration, EventChangeArg, buildEventApis, isInteractionValid,\n} from '@fullcalendar/common'\nimport { __assign } from 'tslib'\nimport { HitDragging, isHitsEqual } from './HitDragging'\nimport { FeaturefulElementDragging } from '../dnd/FeaturefulElementDragging'\n\nexport type EventResizeStartArg = EventResizeStartStopArg\nexport type EventResizeStopArg = EventResizeStartStopArg\n\nexport interface EventResizeStartStopArg {\n  el: HTMLElement\n  event: EventApi\n  jsEvent: MouseEvent\n  view: ViewApi\n}\n\nexport interface EventResizeDoneArg extends EventChangeArg {\n  el: HTMLElement\n  startDelta: Duration\n  endDelta: Duration\n  jsEvent: MouseEvent\n  view: ViewApi\n}\n\nexport class EventResizing extends Interaction {\n  dragging: FeaturefulElementDragging\n  hitDragging: HitDragging\n\n  // internal state\n  draggingSegEl: HTMLElement | null = null\n  draggingSeg: Seg | null = null // TODO: rename to resizingSeg? subjectSeg?\n  eventRange: EventRenderRange | null = null\n  relevantEvents: EventStore | null = null\n  validMutation: EventMutation | null = null\n  mutatedRelevantEvents: EventStore | null = null\n\n  constructor(settings: InteractionSettings) {\n    super(settings)\n    let { component } = settings\n\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el)\n    dragging.pointer.selector = '.fc-event-resizer'\n    dragging.touchScrollAllowed = false\n    dragging.autoScroller.isEnabled = component.context.options.dragScroll\n\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings))\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown)\n    hitDragging.emitter.on('dragstart', this.handleDragStart)\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate)\n    hitDragging.emitter.on('dragend', this.handleDragEnd)\n  }\n\n  destroy() {\n    this.dragging.destroy()\n  }\n\n  handlePointerDown = (ev: PointerDragEvent) => {\n    let { component } = this\n    let segEl = this.querySegEl(ev)\n    let seg = getElSeg(segEl)\n    let eventRange = this.eventRange = seg.eventRange!\n\n    this.dragging.minDistance = component.context.options.eventDragMinDistance\n\n    // if touch, need to be working with a selected event\n    this.dragging.setIgnoreMove(\n      !this.component.isValidSegDownEl(ev.origEvent.target as HTMLElement) ||\n      (ev.isTouch && this.component.props.eventSelection !== eventRange.instance!.instanceId),\n    )\n  }\n\n  handleDragStart = (ev: PointerDragEvent) => {\n    let { context } = this.component\n    let eventRange = this.eventRange!\n\n    this.relevantEvents = getRelevantEvents(\n      context.getCurrentData().eventStore,\n      this.eventRange.instance!.instanceId,\n    )\n\n    let segEl = this.querySegEl(ev)\n    this.draggingSegEl = segEl\n    this.draggingSeg = getElSeg(segEl)\n\n    context.calendarApi.unselect()\n    context.emitter.trigger('eventResizeStart', {\n      el: segEl,\n      event: new EventApi(context, eventRange.def, eventRange.instance),\n      jsEvent: ev.origEvent as MouseEvent, // Is this always a mouse event? See #4655\n      view: context.viewApi,\n    } as EventResizeStartArg)\n  }\n\n  handleHitUpdate = (hit: Hit | null, isFinal: boolean, ev: PointerDragEvent) => {\n    let { context } = this.component\n    let relevantEvents = this.relevantEvents!\n    let initialHit = this.hitDragging.initialHit!\n    let eventInstance = this.eventRange.instance!\n    let mutation: EventMutation | null = null\n    let mutatedRelevantEvents: EventStore | null = null\n    let isInvalid = false\n    let interaction: EventInteractionState = {\n      affectedEvents: relevantEvents,\n      mutatedEvents: createEmptyEventStore(),\n      isEvent: true,\n    }\n\n    if (hit) {\n      let disallowed = hit.componentId === initialHit.componentId\n        && this.isHitComboAllowed\n        && !this.isHitComboAllowed(initialHit, hit)\n\n      if (!disallowed) {\n        mutation = computeMutation(\n          initialHit,\n          hit,\n          (ev.subjectEl as HTMLElement).classList.contains('fc-event-resizer-start'),\n          eventInstance.range,\n        )\n      }\n    }\n\n    if (mutation) {\n      mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, context.getCurrentData().eventUiBases, mutation, context)\n      interaction.mutatedEvents = mutatedRelevantEvents\n\n      if (!isInteractionValid(interaction, hit.dateProfile, context)) {\n        isInvalid = true\n        mutation = null\n        mutatedRelevantEvents = null\n        interaction.mutatedEvents = null\n      }\n    }\n\n    if (mutatedRelevantEvents) {\n      context.dispatch({\n        type: 'SET_EVENT_RESIZE',\n        state: interaction,\n      })\n    } else {\n      context.dispatch({ type: 'UNSET_EVENT_RESIZE' })\n    }\n\n    if (!isInvalid) {\n      enableCursor()\n    } else {\n      disableCursor()\n    }\n\n    if (!isFinal) {\n      if (mutation && isHitsEqual(initialHit, hit)) {\n        mutation = null\n      }\n\n      this.validMutation = mutation\n      this.mutatedRelevantEvents = mutatedRelevantEvents\n    }\n  }\n\n  handleDragEnd = (ev: PointerDragEvent) => {\n    let { context } = this.component\n    let eventDef = this.eventRange!.def\n    let eventInstance = this.eventRange!.instance\n    let eventApi = new EventApi(context, eventDef, eventInstance)\n    let relevantEvents = this.relevantEvents!\n    let mutatedRelevantEvents = this.mutatedRelevantEvents!\n\n    context.emitter.trigger('eventResizeStop', {\n      el: this.draggingSegEl,\n      event: eventApi,\n      jsEvent: ev.origEvent as MouseEvent, // Is this always a mouse event? See #4655\n      view: context.viewApi,\n    } as EventResizeStopArg)\n\n    if (this.validMutation) {\n      let updatedEventApi = new EventApi(\n        context,\n        mutatedRelevantEvents.defs[eventDef.defId],\n        eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null,\n      )\n\n      context.dispatch({\n        type: 'MERGE_EVENTS',\n        eventStore: mutatedRelevantEvents,\n      })\n\n      let eventChangeArg: EventChangeArg = {\n        oldEvent: eventApi,\n        event: updatedEventApi,\n        relatedEvents: buildEventApis(mutatedRelevantEvents, context, eventInstance),\n        revert() {\n          context.dispatch({\n            type: 'MERGE_EVENTS',\n            eventStore: relevantEvents, // the pre-change events\n          })\n        },\n      }\n\n      context.emitter.trigger('eventResize', {\n        ...eventChangeArg,\n        el: this.draggingSegEl,\n        startDelta: this.validMutation.startDelta || createDuration(0),\n        endDelta: this.validMutation.endDelta || createDuration(0),\n        jsEvent: ev.origEvent as MouseEvent,\n        view: context.viewApi,\n      })\n\n      context.emitter.trigger('eventChange', eventChangeArg)\n    } else {\n      context.emitter.trigger('_noEventResize')\n    }\n\n    // reset all internal state\n    this.draggingSeg = null\n    this.relevantEvents = null\n    this.validMutation = null\n\n    // okay to keep eventInstance around. useful to set it in handlePointerDown\n  }\n\n  querySegEl(ev: PointerDragEvent) {\n    return elementClosest(ev.subjectEl as HTMLElement, '.fc-event')\n  }\n}\n\nfunction computeMutation(\n  hit0: Hit,\n  hit1: Hit,\n  isFromStart: boolean,\n  instanceRange: DateRange,\n): EventMutation | null {\n  let dateEnv = hit0.context.dateEnv\n  let date0 = hit0.dateSpan.range.start\n  let date1 = hit1.dateSpan.range.start\n\n  let delta = diffDates(\n    date0, date1,\n    dateEnv,\n    hit0.largeUnit,\n  )\n\n  if (isFromStart) {\n    if (dateEnv.add(instanceRange.start, delta) < instanceRange.end) {\n      return { startDelta: delta }\n    }\n  } else if (dateEnv.add(instanceRange.end, delta) > instanceRange.start) {\n    return { endDelta: delta }\n  }\n\n  return null\n}\n", "import {\n  DateSelection<PERSON><PERSON>,\n  PointerDrag<PERSON>vent,\n  elementClosest,\n  CalendarContext,\n  getEventTargetViaRoot,\n} from '@fullcalendar/common'\nimport { PointerDragging } from '../dnd/PointerDragging'\nimport { EventDragging } from './EventDragging'\n\nexport class UnselectAuto {\n  documentPointer: PointerDragging // for unfocusing\n  isRecentPointerDateSelect = false // wish we could use a selector to detect date selection, but uses hit system\n  matchesCancel = false\n  matchesEvent = false\n\n  constructor(private context: CalendarContext) {\n    let documentPointer = this.documentPointer = new PointerDragging(document)\n    documentPointer.shouldIgnoreMove = true\n    documentPointer.shouldWatchScroll = false\n    documentPointer.emitter.on('pointerdown', this.onDocumentPointerDown)\n    documentPointer.emitter.on('pointerup', this.onDocumentPointerUp)\n\n    /*\n    TODO: better way to know about whether there was a selection with the pointer\n    */\n    context.emitter.on('select', this.onSelect)\n  }\n\n  destroy() {\n    this.context.emitter.off('select', this.onSelect)\n    this.documentPointer.destroy()\n  }\n\n  onSelect = (selectInfo: DateSelectionApi) => {\n    if (selectInfo.jsEvent) {\n      this.isRecentPointerDateSelect = true\n    }\n  }\n\n  onDocumentPointerDown = (pev: PointerDragEvent) => {\n    let unselectCancel = this.context.options.unselectCancel\n    let downEl = getEventTargetViaRoot(pev.origEvent) as HTMLElement\n\n    this.matchesCancel = !!elementClosest(downEl, unselectCancel)\n    this.matchesEvent = !!elementClosest(downEl, EventDragging.SELECTOR) // interaction started on an event?\n  }\n\n  onDocumentPointerUp = (pev: PointerDragEvent) => {\n    let { context } = this\n    let { documentPointer } = this\n    let calendarState = context.getCurrentData()\n\n    // touch-scrolling should never unfocus any type of selection\n    if (!documentPointer.wasTouchScroll) {\n      if (\n        calendarState.dateSelection && // an existing date selection?\n        !this.isRecentPointerDateSelect // a new pointer-initiated date selection since last onDocumentPointerUp?\n      ) {\n        let unselectAuto = context.options.unselectAuto\n\n        if (unselectAuto && (!unselectAuto || !this.matchesCancel)) {\n          context.calendarApi.unselect(pev)\n        }\n      }\n\n      if (\n        calendarState.eventSelection && // an existing event selected?\n        !this.matchesEvent // interaction DIDN'T start on an event\n      ) {\n        context.dispatch({ type: 'UNSELECT_EVENT' })\n      }\n    }\n\n    this.isRecentPointerDateSelect = false\n  }\n}\n", "import { identity, Identity, EventDropArg } from '@fullcalendar/common'\n\n// public\nimport {\n  DateClickArg,\n  EventDragStartArg, EventDragStopArg,\n  EventResizeStartArg, EventResizeStopArg, EventResizeDoneArg,\n  DropArg, EventReceiveArg, EventLeaveArg,\n} from './api-type-deps'\n\nexport const OPTION_REFINERS = {\n  fixedMirrorParent: identity as Identity<HTMLElement>,\n}\n\nexport const LISTENER_REFINERS = {\n  dateClick: identity as Identity<(arg: DateClickArg) => void>,\n  eventDragStart: identity as Identity<(arg: EventDragStartArg) => void>,\n  eventDragStop: identity as Identity<(arg: EventDragStopArg) => void>,\n  eventDrop: identity as Identity<(arg: EventDropArg) => void>,\n  eventResizeStart: identity as Identity<(arg: EventResizeStartArg) => void>,\n  eventResizeStop: identity as Identity<(arg: EventResizeStopArg) => void>,\n  eventResize: identity as Identity<(arg: EventResizeDoneArg) => void>,\n  drop: identity as Identity<(arg: DropArg) => void>,\n  eventReceive: identity as Identity<(arg: EventReceiveArg) => void>,\n  eventLeave: identity as Identity<(arg: EventLeaveArg) => void>,\n}\n", "import {\n  Hit,\n  interactionSettingsStore,\n  PointerDragEvent,\n  parseEventDef, createEventInstance, EventTuple,\n  createEmptyEventStore, eventTupleToStore,\n  config,\n  DateSpan, DatePointApi,\n  EventInteractionState,\n  DragMetaInput, DragMeta, parseDragMeta,\n  EventApi,\n  elementMatches,\n  enableCursor, disableCursor,\n  isInteractionValid,\n  ElementDragging,\n  ViewApi,\n  CalendarContext,\n  getDefaultEventEnd,\n  refineEventDef,\n} from '@fullcalendar/common'\nimport { __assign } from 'tslib'\nimport { HitDragging } from '../interactions/HitDragging'\nimport { buildDatePointApiWithContext } from '../utils'\n\nexport type DragMetaGenerator = DragMetaInput | ((el: HTMLElement) => DragMetaInput)\n\nexport interface ExternalDropApi extends DatePointApi {\n  draggedEl: HTMLElement\n  jsEvent: UIEvent\n  view: ViewApi\n}\n\n/*\nGiven an already instantiated draggable object for one-or-more elements,\nInterprets any dragging as an attempt to drag an events that lives outside\nof a calendar onto a calendar.\n*/\nexport class ExternalElementDragging {\n  hitDragging: HitDragging\n  receivingContext: CalendarContext | null = null\n  droppableEvent: EventTuple | null = null // will exist for all drags, even if create:false\n  suppliedDragMeta: DragMetaGenerator | null = null\n  dragMeta: DragMeta | null = null\n\n  constructor(dragging: ElementDragging, suppliedDragMeta?: DragMetaGenerator) {\n    let hitDragging = this.hitDragging = new HitDragging(dragging, interactionSettingsStore)\n    hitDragging.requireInitial = false // will start outside of a component\n    hitDragging.emitter.on('dragstart', this.handleDragStart)\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate)\n    hitDragging.emitter.on('dragend', this.handleDragEnd)\n\n    this.suppliedDragMeta = suppliedDragMeta\n  }\n\n  handleDragStart = (ev: PointerDragEvent) => {\n    this.dragMeta = this.buildDragMeta(ev.subjectEl as HTMLElement)\n  }\n\n  buildDragMeta(subjectEl: HTMLElement) {\n    if (typeof this.suppliedDragMeta === 'object') {\n      return parseDragMeta(this.suppliedDragMeta)\n    }\n    if (typeof this.suppliedDragMeta === 'function') {\n      return parseDragMeta(this.suppliedDragMeta(subjectEl))\n    }\n    return getDragMetaFromEl(subjectEl)\n  }\n\n  handleHitUpdate = (hit: Hit | null, isFinal: boolean, ev: PointerDragEvent) => {\n    let { dragging } = this.hitDragging\n    let receivingContext: CalendarContext | null = null\n    let droppableEvent: EventTuple | null = null\n    let isInvalid = false\n    let interaction: EventInteractionState = {\n      affectedEvents: createEmptyEventStore(),\n      mutatedEvents: createEmptyEventStore(),\n      isEvent: this.dragMeta!.create,\n    }\n\n    if (hit) {\n      receivingContext = hit.context\n\n      if (this.canDropElOnCalendar(ev.subjectEl as HTMLElement, receivingContext)) {\n        droppableEvent = computeEventForDateSpan(\n          hit.dateSpan,\n          this.dragMeta!,\n          receivingContext,\n        )\n\n        interaction.mutatedEvents = eventTupleToStore(droppableEvent)\n        isInvalid = !isInteractionValid(interaction, hit.dateProfile, receivingContext)\n\n        if (isInvalid) {\n          interaction.mutatedEvents = createEmptyEventStore()\n          droppableEvent = null\n        }\n      }\n    }\n\n    this.displayDrag(receivingContext, interaction)\n\n    // show mirror if no already-rendered mirror element OR if we are shutting down the mirror (?)\n    // TODO: wish we could somehow wait for dispatch to guarantee render\n    dragging.setMirrorIsVisible(\n      isFinal || !droppableEvent || !document.querySelector('.fc-event-mirror'), // TODO: turn className into constant\n      // TODO: somehow query FullCalendars WITHIN shadow-roots for existing event-mirror els\n    )\n\n    if (!isInvalid) {\n      enableCursor()\n    } else {\n      disableCursor()\n    }\n\n    if (!isFinal) {\n      dragging.setMirrorNeedsRevert(!droppableEvent)\n\n      this.receivingContext = receivingContext\n      this.droppableEvent = droppableEvent\n    }\n  }\n\n  handleDragEnd = (pev: PointerDragEvent) => {\n    let { receivingContext, droppableEvent } = this\n\n    this.clearDrag()\n\n    if (receivingContext && droppableEvent) {\n      let finalHit = this.hitDragging.finalHit!\n      let finalView = finalHit.context.viewApi\n      let dragMeta = this.dragMeta!\n\n      receivingContext.emitter.trigger('drop', {\n        ...buildDatePointApiWithContext(finalHit.dateSpan, receivingContext),\n        draggedEl: pev.subjectEl as HTMLElement,\n        jsEvent: pev.origEvent as MouseEvent, // Is this always a mouse event? See #4655\n        view: finalView,\n      })\n\n      if (dragMeta.create) {\n        let addingEvents = eventTupleToStore(droppableEvent)\n\n        receivingContext.dispatch({\n          type: 'MERGE_EVENTS',\n          eventStore: addingEvents,\n        })\n\n        if (pev.isTouch) {\n          receivingContext.dispatch({\n            type: 'SELECT_EVENT',\n            eventInstanceId: droppableEvent.instance.instanceId,\n          })\n        }\n\n        // signal that an external event landed\n        receivingContext.emitter.trigger('eventReceive', {\n          event: new EventApi(\n            receivingContext,\n            droppableEvent.def,\n            droppableEvent.instance,\n          ),\n          relatedEvents: [],\n          revert() {\n            receivingContext.dispatch({\n              type: 'REMOVE_EVENTS',\n              eventStore: addingEvents,\n            })\n          },\n          draggedEl: pev.subjectEl as HTMLElement,\n          view: finalView,\n        })\n      }\n    }\n\n    this.receivingContext = null\n    this.droppableEvent = null\n  }\n\n  displayDrag(nextContext: CalendarContext | null, state: EventInteractionState) {\n    let prevContext = this.receivingContext\n\n    if (prevContext && prevContext !== nextContext) {\n      prevContext.dispatch({ type: 'UNSET_EVENT_DRAG' })\n    }\n\n    if (nextContext) {\n      nextContext.dispatch({ type: 'SET_EVENT_DRAG', state })\n    }\n  }\n\n  clearDrag() {\n    if (this.receivingContext) {\n      this.receivingContext.dispatch({ type: 'UNSET_EVENT_DRAG' })\n    }\n  }\n\n  canDropElOnCalendar(el: HTMLElement, receivingContext: CalendarContext): boolean {\n    let dropAccept = receivingContext.options.dropAccept\n\n    if (typeof dropAccept === 'function') {\n      return dropAccept.call(receivingContext.calendarApi, el)\n    }\n\n    if (typeof dropAccept === 'string' && dropAccept) {\n      return Boolean(elementMatches(el, dropAccept))\n    }\n\n    return true\n  }\n}\n\n// Utils for computing event store from the DragMeta\n// ----------------------------------------------------------------------------------------------------\n\nfunction computeEventForDateSpan(dateSpan: DateSpan, dragMeta: DragMeta, context: CalendarContext): EventTuple {\n  let defProps = { ...dragMeta.leftoverProps }\n\n  for (let transform of context.pluginHooks.externalDefTransforms) {\n    __assign(defProps, transform(dateSpan, dragMeta))\n  }\n\n  let { refined, extra } = refineEventDef(defProps, context)\n  let def = parseEventDef(\n    refined,\n    extra,\n    dragMeta.sourceId,\n    dateSpan.allDay,\n    context.options.forceEventDuration || Boolean(dragMeta.duration), // hasEnd\n    context,\n  )\n\n  let start = dateSpan.range.start\n\n  // only rely on time info if drop zone is all-day,\n  // otherwise, we already know the time\n  if (dateSpan.allDay && dragMeta.startTime) {\n    start = context.dateEnv.add(start, dragMeta.startTime)\n  }\n\n  let end = dragMeta.duration ?\n    context.dateEnv.add(start, dragMeta.duration) :\n    getDefaultEventEnd(dateSpan.allDay, start, context)\n\n  let instance = createEventInstance(def.defId, { start, end })\n\n  return { def, instance }\n}\n\n// Utils for extracting data from element\n// ----------------------------------------------------------------------------------------------------\n\nfunction getDragMetaFromEl(el: HTMLElement): DragMeta {\n  let str = getEmbeddedElData(el, 'event')\n  let obj = str ?\n    JSON.parse(str) :\n    { create: false } // if no embedded data, assume no event creation\n\n  return parseDragMeta(obj)\n}\n\nconfig.dataAttrPrefix = ''\n\nfunction getEmbeddedElData(el: HTMLElement, name: string): string {\n  let prefix = config.dataAttrPrefix\n  let prefixedName = (prefix ? prefix + '-' : '') + name\n\n  return el.getAttribute('data-' + prefixedName) || ''\n}\n", "import { BASE_OPTION_DEFAULTS, PointerDragEvent } from '@fullcalendar/common'\nimport { FeaturefulElementDragging } from '../dnd/FeaturefulElementDragging'\nimport { ExternalElementDragging, DragMetaGenerator } from './ExternalElementDragging'\n\nexport interface ExternalDraggableSettings {\n  eventData?: DragMetaGenerator\n  itemSelector?: string\n  minDistance?: number\n  longPressDelay?: number\n  appendTo?: HTMLElement\n}\n\n/*\nMakes an element (that is *external* to any calendar) draggable.\nCan pass in data that determines how an event will be created when dropped onto a calendar.\nLeverages FullCalendar's internal drag-n-drop functionality WITHOUT a third-party drag system.\n*/\nexport class ExternalDraggable {\n  dragging: FeaturefulElementDragging\n  settings: ExternalDraggableSettings\n\n  constructor(el: HTMLElement, settings: ExternalDraggableSettings = {}) {\n    this.settings = settings\n\n    let dragging = this.dragging = new FeaturefulElementDragging(el)\n    dragging.touchScrollAllowed = false\n\n    if (settings.itemSelector != null) {\n      dragging.pointer.selector = settings.itemSelector\n    }\n\n    if (settings.appendTo != null) {\n      dragging.mirror.parentNode = settings.appendTo // TODO: write tests\n    }\n\n    dragging.emitter.on('pointerdown', this.handlePointerDown)\n    dragging.emitter.on('dragstart', this.handleDragStart)\n\n    new ExternalElementDragging(dragging, settings.eventData) // eslint-disable-line no-new\n  }\n\n  handlePointerDown = (ev: PointerDragEvent) => {\n    let { dragging } = this\n    let { minDistance, longPressDelay } = this.settings\n\n    dragging.minDistance =\n      minDistance != null ?\n        minDistance :\n        (ev.isTouch ? 0 : BASE_OPTION_DEFAULTS.eventDragMinDistance)\n\n    dragging.delay =\n      ev.isTouch ? // TODO: eventually read eventLongPressDelay instead vvv\n        (longPressDelay != null ? longPressDelay : BASE_OPTION_DEFAULTS.longPressDelay) :\n        0\n  }\n\n  handleDragStart = (ev: PointerDragEvent) => {\n    if (\n      ev.isTouch &&\n      this.dragging.delay &&\n      (ev.subjectEl as HTMLElement).classList.contains('fc-event')\n    ) {\n      this.dragging.mirror.getMirrorEl().classList.add('fc-event-selected')\n    }\n  }\n\n  destroy() {\n    this.dragging.destroy()\n  }\n}\n", "import { PointerDragEvent, ElementDragging } from '@fullcalendar/common'\nimport { PointerDragging } from '../dnd/PointerDragging'\n\n/*\nDetects when a *THIRD-PARTY* drag-n-drop system interacts with elements.\nThe third-party system is responsible for drawing the visuals effects of the drag.\nThis class simply monitors for pointer movements and fires events.\nIt also has the ability to hide the moving element (the \"mirror\") during the drag.\n*/\nexport class InferredElementDragging extends ElementDragging {\n  pointer: PointerDragging\n  shouldIgnoreMove: boolean = false\n  mirrorSelector: string = ''\n  currentMirrorEl: HTMLElement | null = null\n\n  constructor(containerEl: HTMLElement) {\n    super(containerEl)\n\n    let pointer = this.pointer = new PointerDragging(containerEl)\n    pointer.emitter.on('pointerdown', this.handlePointerDown)\n    pointer.emitter.on('pointermove', this.handlePointerMove)\n    pointer.emitter.on('pointerup', this.handlePointerUp)\n  }\n\n  destroy() {\n    this.pointer.destroy()\n  }\n\n  handlePointerDown = (ev: PointerDragEvent) => {\n    this.emitter.trigger('pointerdown', ev)\n\n    if (!this.shouldIgnoreMove) {\n      // fire dragstart right away. does not support delay or min-distance\n      this.emitter.trigger('dragstart', ev)\n    }\n  }\n\n  handlePointerMove = (ev: PointerDragEvent) => {\n    if (!this.shouldIgnoreMove) {\n      this.emitter.trigger('dragmove', ev)\n    }\n  }\n\n  handlePointerUp = (ev: PointerDragEvent) => {\n    this.emitter.trigger('pointerup', ev)\n\n    if (!this.shouldIgnoreMove) {\n      // fire dragend right away. does not support a revert animation\n      this.emitter.trigger('dragend', ev)\n    }\n  }\n\n  setIgnoreMove(bool: boolean) {\n    this.shouldIgnoreMove = bool\n  }\n\n  setMirrorIsVisible(bool: boolean) {\n    if (bool) {\n      // restore a previously hidden element.\n      // use the reference in case the selector class has already been removed.\n      if (this.currentMirrorEl) {\n        this.currentMirrorEl.style.visibility = ''\n        this.currentMirrorEl = null\n      }\n    } else {\n      let mirrorEl = this.mirrorSelector\n        // TODO: somehow query FullCalendars WITHIN shadow-roots\n        ? document.querySelector(this.mirrorSelector) as HTMLElement\n        : null\n\n      if (mirrorEl) {\n        this.currentMirrorEl = mirrorEl\n        mirrorEl.style.visibility = 'hidden'\n      }\n    }\n  }\n}\n", "import { ExternalElementDragging, DragMetaGenerator } from './ExternalElementDragging'\nimport { InferredElementDragging } from './InferredElementDragging'\n\nexport interface ThirdPartyDraggableSettings {\n  eventData?: DragMetaGenerator\n  itemSelector?: string\n  mirrorSelector?: string\n}\n\n/*\nBridges third-party drag-n-drop systems with FullCalendar.\nMust be instantiated and destroyed by caller.\n*/\nexport class ThirdPartyDraggable {\n  dragging: InferredElementDragging\n\n  constructor(\n    containerOrSettings?: EventTarget | ThirdPartyDraggableSettings,\n    settings?: ThirdPartyDraggableSettings,\n  ) {\n    let containerEl: EventTarget = document\n\n    if (\n      // wish we could just test instanceof EventTarget, but doesn't work in IE11\n      containerOrSettings === document ||\n      containerOrSettings instanceof Element\n    ) {\n      containerEl = containerOrSettings as EventTarget\n      settings = settings || {}\n    } else {\n      settings = (containerOrSettings || {}) as ThirdPartyDraggableSettings\n    }\n\n    let dragging = this.dragging = new InferredElementDragging(containerEl as HTMLElement)\n\n    if (typeof settings.itemSelector === 'string') {\n      dragging.pointer.selector = settings.itemSelector\n    } else if (containerEl === document) {\n      dragging.pointer.selector = '[data-event]'\n    }\n\n    if (typeof settings.mirrorSelector === 'string') {\n      dragging.mirrorSelector = settings.mirrorSelector\n    }\n\n    new ExternalElementDragging(dragging, settings.eventData) // eslint-disable-line no-new\n  }\n\n  destroy() {\n    this.dragging.destroy()\n  }\n}\n", "import { createPlugin } from '@fullcalendar/common'\nimport { DateClicking } from './interactions/DateClicking'\nimport { DateSelecting } from './interactions/DateSelecting'\nimport { EventDragging } from './interactions/EventDragging'\nimport { EventResizing } from './interactions/EventResizing'\nimport { UnselectAuto } from './interactions/UnselectAuto'\nimport { FeaturefulElementDragging } from './dnd/FeaturefulElementDragging'\nimport { OPTION_REFINERS, LISTENER_REFINERS } from './options'\nimport './options-declare'\n\nexport default createPlugin({\n  componentInteractions: [DateClicking, DateSelecting, EventDragging, EventResizing],\n  calendarInteractions: [UnselectAuto],\n  elementDraggingImpl: FeaturefulElementDragging,\n  optionRefiners: OPTION_REFINERS,\n  listenerRefiners: LISTENER_REFINERS,\n})\n\nexport * from './api-type-deps'\nexport { FeaturefulElementDragging }\nexport { PointerDragging } from './dnd/PointerDragging'\nexport { ExternalDraggable as Draggable } from './interactions-external/ExternalDraggable'\nexport { ThirdPartyDraggable } from './interactions-external/ThirdPartyDraggable'\n"], "names": ["getComponentTouchDelay"], "mappings": ";;;;;;;;AAEA,MAAM,CAAC,oBAAoB,GAAG,GAAG,CAAA;AAEjC,IAAI,gBAAgB,GAAG,CAAC,CAAA;AACxB,IAAI,WAAW,GAAG,CAAC,CAAA;AACnB,IAAI,0BAA0B,GAAG,KAAK,CAAA;AAEtC;;;;;;;;;;;;;;IAmCE,yBAAY,WAAwB;QAApC,iBAMC;QA1BD,cAAS,GAAuB,IAAI,CAAA;;QAIpC,aAAQ,GAAW,EAAE,CAAA;QACrB,mBAAc,GAAW,EAAE,CAAA;QAC3B,qBAAgB,GAAY,KAAK,CAAA;QACjC,sBAAiB,GAAY,IAAI,CAAA;;QAGjC,eAAU,GAAY,KAAK,CAAA;QAC3B,oBAAe,GAAY,KAAK,CAAA;QAChC,mBAAc,GAAY,KAAK,CAAA;;;QA0D/B,oBAAe,GAAG,UAAC,EAAc;YAC/B,IACE,CAAC,KAAI,CAAC,iBAAiB,EAAE;gBACzB,oBAAoB,CAAC,EAAE,CAAC;gBACxB,KAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EACjB;gBACA,IAAI,GAAG,GAAG,KAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;gBAC7C,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAA;gBACxC,KAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;gBAEzB,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;oBAC1B,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;iBAC7D;gBAED,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,KAAI,CAAC,aAAa,CAAC,CAAA;aACzD;SACF,CAAA;QAED,oBAAe,GAAG,UAAC,EAAc;YAC/B,IAAI,GAAG,GAAG,KAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAA;YACvC,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;YACtB,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAA;SACzC,CAAA;QAED,kBAAa,GAAG,UAAC,EAAc;YAC7B,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;YAC/D,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,KAAI,CAAC,aAAa,CAAC,CAAA;YAE3D,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAA;YAEhE,KAAI,CAAC,OAAO,EAAE,CAAA;SACf,CAAA;;;QASD,qBAAgB,GAAG,UAAC,EAAc;YAChC,IAAI,KAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;gBACrB,KAAI,CAAC,eAAe,GAAG,IAAI,CAAA;gBAE3B,IAAI,GAAG,GAAG,KAAI,CAAC,oBAAoB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;gBAC7C,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAA;gBACxC,KAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;;;gBAIzB,IAAI,QAAQ,GAAG,EAAE,CAAC,MAAqB,CAAA;gBAEvC,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;oBAC1B,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;iBAC7D;gBAED,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,KAAI,CAAC,cAAc,CAAC,CAAA;gBAC1D,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,KAAI,CAAC,cAAc,CAAC,CAAA;;;;gBAK7D,MAAM,CAAC,gBAAgB,CACrB,QAAQ,EACR,KAAI,CAAC,iBAAiB,EACtB,IAAI,CACL,CAAA;aACF;SACF,CAAA;QAED,oBAAe,GAAG,UAAC,EAAc;YAC/B,IAAI,GAAG,GAAG,KAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAA;YACvC,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;YACtB,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAA;SACzC,CAAA;QAED,mBAAc,GAAG,UAAC,EAAc;YAC9B,IAAI,KAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,QAAQ,GAAG,EAAE,CAAC,MAAqB,CAAA;gBAEvC,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;gBAC/D,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,KAAI,CAAC,cAAc,CAAC,CAAA;gBAC7D,QAAQ,CAAC,mBAAmB,CAAC,aAAa,EAAE,KAAI,CAAC,cAAc,CAAC,CAAA;gBAChE,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAA;gBAElE,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAA;gBAEhE,KAAI,CAAC,OAAO,EAAE,CAAA;gBACd,KAAI,CAAC,eAAe,GAAG,KAAK,CAAA;gBAC5B,kBAAkB,EAAE,CAAA;aACrB;SACF,CAAA;QAED,sBAAiB,GAAG;YAClB,KAAI,CAAC,cAAc,GAAG,IAAI,CAAA;SAC3B,CAAA;QA4BD,iBAAY,GAAG,UAAC,EAAW;YACzB,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;gBAC1B,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,KAAI,CAAC,WAAW,IAAI,KAAI,CAAC,SAAS,CAAA;gBACpE,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,KAAI,CAAC,WAAW,IAAI,KAAI,CAAC,SAAS,CAAA;gBAEpE,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE;oBAClC,SAAS,EAAE,EAAE;oBACb,OAAO,EAAE,KAAI,CAAC,eAAe;oBAC7B,SAAS,EAAE,KAAI,CAAC,SAAS;oBACzB,KAAK,OAAA;oBACL,KAAK,OAAA;oBACL,MAAM,EAAE,KAAK,GAAG,KAAI,CAAC,SAAS;oBAC9B,MAAM,EAAE,KAAK,GAAG,KAAI,CAAC,SAAS;iBACX,CAAC,CAAA;aACvB;SACF,CAAA;QA3LC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,EAAE,CAAA;QAC5B,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,eAAgC,CAAC,CAAA;QAChF,WAAW,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAiC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;QACrG,eAAe,EAAE,CAAA;KAClB;IAED,iCAAO,GAAP;QACE,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,eAAgC,CAAC,CAAA;QACxF,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAiC,EAAE,EAAE,OAAO,EAAE,IAAI,EAA6B,CAAC,CAAA;QACxI,iBAAiB,EAAE,CAAA;KACpB;IAED,kCAAQ,GAAR,UAAS,EAAW;QAClB,IAAI,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;QACvC,IAAI,MAAM,GAAG,EAAE,CAAC,MAAqB,CAAA;QAErC,IACE,SAAS;aACR,CAAC,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EACrE;YACA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YACtB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAA;YAE3B,OAAO,IAAI,CAAA;SACZ;QAED,OAAO,KAAK,CAAA;KACb;IAED,iCAAO,GAAP;QACE,0BAA0B,GAAG,KAAK,CAAA;QAClC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;;QAErB,IAAI,CAAC,kBAAkB,EAAE,CAAA;KAC1B;IAED,wCAAc,GAAd,UAAe,EAAW;QACxB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,cAAc,CAAC,EAAE,CAAC,MAAqB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC/D;QACD,OAAO,IAAI,CAAC,WAA0B,CAAA;KACvC;IAsCD,2CAAiB,GAAjB;QACE,OAAO,gBAAgB,IAAI,IAAI,CAAC,eAAe,CAAA;KAChD;;IA+DD,2CAAiB,GAAjB;QACE,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,0BAA0B,GAAG,IAAI,CAAA;SAClC;KACF;;;IAKD,yCAAe,GAAf,UAAgB,EAAoB;QAClC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;YACrB,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;SAC3D;KACF;IAED,sCAAY,GAAZ,UAAa,EAAoB;QAC/B,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,SAAS,GAAI,EAAU,CAAC,KAAK,CAAA;YAClC,IAAI,CAAC,SAAS,GAAI,EAAU,CAAC,KAAK,CAAA;YAClC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAA;YACrC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAA;SACtC;KACF;IAmBD,4CAAkB,GAAlB;QACE,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;SAC9D;KACF;;;IAKD,8CAAoB,GAApB,UAAqB,EAAc,EAAE,OAAiB;QACpD,IAAI,MAAM,GAAG,CAAC,CAAA;QACd,IAAI,MAAM,GAAG,CAAC,CAAA;;QAGd,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,KAAK,CAAA;YACzB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,KAAK,CAAA;SAC1B;aAAM;YACL,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;YAClC,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;SACnC;QAED,OAAO;YACL,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,KAAK,EAAE,EAAE,CAAC,KAAK;YACf,KAAK,EAAE,EAAE,CAAC,KAAK;YACf,MAAM,QAAA;YACN,MAAM,QAAA;SACP,CAAA;KACF;IAED,8CAAoB,GAApB,UAAqB,EAAc,EAAE,OAAiB;QACpD,IAAI,OAAO,GAAG,EAAE,CAAC,OAAO,CAAA;QACxB,IAAI,KAAK,CAAA;QACT,IAAI,KAAK,CAAA;QACT,IAAI,MAAM,GAAG,CAAC,CAAA;QACd,IAAI,MAAM,GAAG,CAAC,CAAA;;;QAId,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;YAC7B,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;YACxB,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;SACzB;aAAM;YACL,KAAK,GAAI,EAAU,CAAC,KAAK,CAAA;YACzB,KAAK,GAAI,EAAU,CAAC,KAAK,CAAA;SAC1B;;QAGD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;YACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;SACvB;aAAM;YACL,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;YAC/B,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;SAChC;QAED,OAAO;YACL,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,KAAK,OAAA;YACL,KAAK,OAAA;YACL,MAAM,QAAA;YACN,MAAM,QAAA;SACP,CAAA;KACF;IACH,sBAAC;AAAD,CAAC,IAAA;AAED;AACA,SAAS,oBAAoB,CAAC,EAAc;IAC1C,OAAO,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAA;AACvC,CAAC;AAED;AACA;AAEA,SAAS,kBAAkB;IACzB,gBAAgB,IAAI,CAAC,CAAA;IAErB,UAAU,CAAC;QACT,gBAAgB,IAAI,CAAC,CAAA;KACtB,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAA;AACjC,CAAC;AAED;AACA;AAEA,SAAS,eAAe;IACtB,WAAW,IAAI,CAAC,CAAA;IAEhB,IAAI,WAAW,KAAK,CAAC,EAAE;QACrB,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;KAC5E;AACH,CAAC;AAED,SAAS,iBAAiB;IACxB,WAAW,IAAI,CAAC,CAAA;IAEhB,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,iBAAiB,EAAE,EAAE,OAAO,EAAE,KAAK,EAA6B,CAAC,CAAA;KAC1G;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,EAAW;IACpC,IAAI,0BAA0B,EAAE;QAC9B,EAAE,CAAC,cAAc,EAAE,CAAA;KACpB;AACH;;ACrVA;;;;;AAKA;IAAA;QACE,cAAS,GAAY,KAAK,CAAA;QAK1B,aAAQ,GAAuB,IAAI,CAAA;QACnC,aAAQ,GAAuB,IAAI,CAAA;QACnC,iBAAY,GAAgB,IAAI,CAAA;;QAGhC,eAAU,GAAgB,QAAQ,CAAC,IAAI,CAAA;QACvC,WAAM,GAAW,IAAI,CAAA;QACrB,mBAAc,GAAW,CAAC,CAAA;KA6H3B;IA3HC,6BAAK,GAAL,UAAM,QAAqB,EAAE,KAAa,EAAE,KAAa;QACvD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAA;QACzD,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,MAAM,CAAC,WAAW,CAAA;QAC7C,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,MAAM,CAAC,WAAW,CAAA;QAC7C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACf,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QACf,IAAI,CAAC,gBAAgB,EAAE,CAAA;KACxB;IAED,kCAAU,GAAV,UAAW,KAAa,EAAE,KAAa;QACrC,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,WAAY,CAAA;QAC9D,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,WAAY,CAAA;QAC9D,IAAI,CAAC,gBAAgB,EAAE,CAAA;KACxB;;IAGD,oCAAY,GAAZ,UAAa,IAAa;QACxB,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAA;iBACjC;gBAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;gBACrB,IAAI,CAAC,gBAAgB,EAAE,CAAA;aACxB;SACF;aAAM,IAAI,IAAI,CAAC,SAAS,EAAE;YACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAA;aACrC;YAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;SACtB;KACF;;IAGD,4BAAI,GAAJ,UAAK,oBAA6B,EAAE,QAAoB;QAAxD,iBAiBC;QAhBC,IAAI,IAAI,GAAG;YACT,KAAI,CAAC,OAAO,EAAE,CAAA;YACd,QAAQ,EAAE,CAAA;SACX,CAAA;QAED,IACE,oBAAoB;YACpB,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,cAAc;aAClB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;UAC5B;YACA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;SAClD;aAAM;YACL,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;SACpB;KACF;IAED,yCAAiB,GAAjB,UAAkB,QAAoB,EAAE,cAAsB;QAC5D,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAS,CAAA;QAC7B,IAAI,iBAAiB,GAAG,IAAI,CAAC,QAAS,CAAC,qBAAqB,EAAE,CAAA;QAE9D,QAAQ,CAAC,KAAK,CAAC,UAAU;YACvB,MAAM,GAAG,cAAc,GAAG,KAAK;gBAC/B,OAAO,GAAG,cAAc,GAAG,IAAI,CAAA;QAEjC,UAAU,CAAC,QAAQ,EAAE;YACnB,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,GAAG,EAAE,iBAAiB,CAAC,GAAG;SAC3B,CAAC,CAAA;QAEF,kBAAkB,CAAC,QAAQ,EAAE;YAC3B,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAA;YAC9B,QAAQ,EAAE,CAAA;SACX,CAAC,CAAA;KACH;IAED,+BAAO,GAAP;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;SACrB;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;KACrB;IAED,wCAAgB,GAAhB;QACE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;gBAC7B,IAAI,EAAE,IAAI,CAAC,YAAa,CAAC,IAAI,GAAG,IAAI,CAAC,MAAO;gBAC5C,GAAG,EAAE,IAAI,CAAC,YAAa,CAAC,GAAG,GAAG,IAAI,CAAC,MAAO;aAC3C,CAAC,CAAA;SACH;KACF;IAED,mCAAW,GAAX;QACE,IAAI,YAAY,GAAG,IAAI,CAAC,YAAa,CAAA;QACrC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAE5B,IAAI,CAAC,QAAQ,EAAE;YACb,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAS,CAAC,SAAS,CAAC,IAAI,CAAgB,CAAA;;;YAIxE,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;YAEzC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA;YAE3C,UAAU,CAAC,QAAQ,EAAE;gBACnB,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,UAAU,EAAE,EAAE;gBACd,SAAS,EAAE,YAAY;gBACvB,KAAK,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI;gBAC7C,MAAM,EAAE,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,GAAG;gBAC9C,KAAK,EAAE,MAAM;gBACb,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,CAAC;aACV,CAAC,CAAA;YAEF,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;SACtC;QAED,OAAO,QAAQ,CAAA;KAChB;IACH,oBAAC;AAAD,CAAC;;AC/ID;;;;;;;;AAQA;IAA8C,mCAAgB;IAc5D,yBAAY,gBAAkC,EAAE,aAAsB;QAAtE,YACE,iBAAO,SAcR;QAWD,kBAAY,GAAG;YACb,KAAI,CAAC,SAAS,GAAG,KAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAA;YACrD,KAAI,CAAC,UAAU,GAAG,KAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAA;YACvD,KAAI,CAAC,kBAAkB,EAAE,CAAA;SAC1B,CAAA;QA5BC,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;QACxC,KAAI,CAAC,aAAa,GAAG,aAAa,CAAA;QAClC,KAAI,CAAC,SAAS,GAAG,KAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC,YAAY,EAAE,CAAA;QACrE,KAAI,CAAC,UAAU,GAAG,KAAI,CAAC,cAAc,GAAG,gBAAgB,CAAC,aAAa,EAAE,CAAA;QACxE,KAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC,cAAc,EAAE,CAAA;QACpD,KAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,eAAe,EAAE,CAAA;QACtD,KAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC,cAAc,EAAE,CAAA;QACpD,KAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,eAAe,EAAE,CAAA;QACtD,KAAI,CAAC,UAAU,GAAG,KAAI,CAAC,iBAAiB,EAAE,CAAA;QAE1C,IAAI,KAAI,CAAC,aAAa,EAAE;YACtB,KAAI,CAAC,cAAc,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAI,CAAC,YAAY,CAAC,CAAA;SACpE;;KACF;IAKD,iCAAO,GAAP;QACE,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;SACvE;KACF;IAQD,sCAAY,GAAZ;QACE,OAAO,IAAI,CAAC,SAAS,CAAA;KACtB;IAED,uCAAa,GAAb;QACE,OAAO,IAAI,CAAC,UAAU,CAAA;KACvB;IAED,sCAAY,GAAZ,UAAa,GAAW;QACtB,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QAEvC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;;YAGvB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;YAEnE,IAAI,CAAC,kBAAkB,EAAE,CAAA;SAC1B;KACF;IAED,uCAAa,GAAb,UAAc,GAAW;QACvB,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;QAExC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;;YAGvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;YAErE,IAAI,CAAC,kBAAkB,EAAE,CAAA;SAC1B;KACF;IAED,wCAAc,GAAd;QACE,OAAO,IAAI,CAAC,WAAW,CAAA;KACxB;IAED,yCAAe,GAAf;QACE,OAAO,IAAI,CAAC,YAAY,CAAA;KACzB;IAED,wCAAc,GAAd;QACE,OAAO,IAAI,CAAC,WAAW,CAAA;KACxB;IAED,yCAAe,GAAf;QACE,OAAO,IAAI,CAAC,YAAY,CAAA;KACzB;IAED,4CAAkB,GAAlB;KACC;IACH,sBAAC;AAAD,CAhGA,CAA8C,gBAAgB;;ACP9D;IAA4C,0CAAe;IACzD,gCAAY,EAAe,EAAE,aAAsB;eACjD,kBAAM,IAAI,uBAAuB,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC;KACtD;IAED,+CAAc,GAAd;QACE,OAAQ,IAAI,CAAC,gBAA4C,CAAC,EAAE,CAAA;KAC7D;IAED,kDAAiB,GAAjB;QACE,OAAO,gBAAgB,CAAE,IAAI,CAAC,gBAA4C,CAAC,EAAE,CAAC,CAAA;KAC/E;IACH,6BAAC;AAAD,CAZA,CAA4C,eAAe;;ACA3D;IAA2C,yCAAe;IACxD,+BAAY,aAAsB;eAChC,kBAAM,IAAI,sBAAsB,EAAE,EAAE,aAAa,CAAC;KACnD;IAED,8CAAc,GAAd;QACE,OAAO,MAAM,CAAA;KACd;IAED,iDAAiB,GAAjB;QACE,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW;YACzC,GAAG,EAAE,IAAI,CAAC,SAAS;YACnB,MAAM,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY;SAC3C,CAAA;KACF;;;IAID,kDAAkB,GAAlB;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;KAC3C;IACH,4BAAC;AAAD,CAvBA,CAA2C,eAAe;;ACQ1D;AACA;AACA;AACA,IAAM,OAAO,GAAG,OAAO,WAAW,KAAK,UAAU,GAAI,WAAmB,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;AAEvF;;;;;;AAMA;IAAA;QAAA,iBAkMC;;QAhMC,cAAS,GAAY,IAAI,CAAA;QACzB,gBAAW,GAAwB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;QAC3D,kBAAa,GAAW,EAAE,CAAA;QAC1B,gBAAW,GAAW,GAAG,CAAA;;QAGzB,mBAAc,GAAkB,IAAI,CAAA;QACpC,mBAAc,GAAkB,IAAI,CAAA;QACpC,gBAAW,GAAY,KAAK,CAAA;QAC5B,iBAAY,GAA6B,IAAI,CAAA;;QAI7C,gBAAW,GAAY,KAAK,CAAA;QAC5B,kBAAa,GAAY,KAAK,CAAA;QAC9B,kBAAa,GAAY,KAAK,CAAA;QAC9B,mBAAc,GAAY,KAAK,CAAA;QA8DvB,YAAO,GAAG;YAChB,IAAI,KAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,IAAI,GAAG,KAAI,CAAC,eAAe,CAC7B,KAAI,CAAC,cAAe,GAAG,MAAM,CAAC,WAAW,EACzC,KAAI,CAAC,cAAe,GAAG,MAAM,CAAC,WAAW,CAC1C,CAAA;gBAED,IAAI,IAAI,EAAE;oBACR,IAAI,GAAG,GAAG,OAAO,EAAE,CAAA;oBACnB,KAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,GAAG,KAAI,CAAC,cAAe,IAAI,IAAI,CAAC,CAAA;oBAC1D,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAA;iBAC3B;qBAAM;oBACL,KAAI,CAAC,WAAW,GAAG,KAAK,CAAA;iBACzB;aACF;SACF,CAAA;KAmGF;IA9KC,4BAAK,GAAL,UAAM,KAAa,EAAE,KAAa,EAAE,aAA0B;QAC5D,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAA;YACnD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YACxB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;YAC1B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;YAC1B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAA;YAC3B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;SAC9B;KACF;IAED,iCAAU,GAAV,UAAW,KAAa,EAAE,KAAa;QACrC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,cAAc,GAAG,KAAK,GAAG,MAAM,CAAC,WAAW,CAAA;YAC/C,IAAI,cAAc,GAAG,KAAK,GAAG,MAAM,CAAC,WAAW,CAAA;YAE/C,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,KAAK,IAAI,GAAG,CAAC,GAAG,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;YACpF,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,KAAK,IAAI,GAAG,CAAC,GAAG,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;YAEpF,IAAI,MAAM,GAAG,CAAC,EAAE;gBACd,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;aACxB;iBAAM,IAAI,MAAM,GAAG,CAAC,EAAE;gBACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;aAC1B;YAED,IAAI,MAAM,GAAG,CAAC,EAAE;gBACd,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;aAC1B;iBAAM,IAAI,MAAM,GAAG,CAAC,EAAE;gBACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;aAC3B;YAED,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;YACpC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;YAEpC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;gBACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAA;aACjC;SACF;KACF;IAED,2BAAI,GAAJ;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YAExB,KAAwB,UAAkB,EAAlB,KAAA,IAAI,CAAC,YAAa,EAAlB,cAAkB,EAAlB,IAAkB,EAAE;gBAAvC,IAAI,WAAW,SAAA;gBAClB,WAAW,CAAC,OAAO,EAAE,CAAA;aACtB;YAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;SACzB;KACF;IAED,uCAAgB,GAAhB,UAAiB,GAAW;QAC1B,IAAI,CAAC,cAAc,GAAG,GAAG,CAAA;QACzB,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;KACpC;IAmBO,iCAAU,GAAlB,UAAmB,IAAU,EAAE,OAAe;QACtC,IAAA,WAAW,GAAK,IAAI,YAAT,CAAS;QACpB,IAAA,aAAa,GAAK,IAAI,cAAT,CAAS;QAC5B,IAAI,WAAW,GAAG,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC/C,IAAI,QAAQ;SACV,CAAC,CAAC,WAAW,GAAG,WAAW,KAAK,aAAa,GAAG,aAAa,CAAC;YAC9D,IAAI,CAAC,WAAW,GAAG,OAAO,CAAA;QAC5B,IAAI,IAAI,GAAG,CAAC,CAAA;QAEZ,QAAQ,IAAI,CAAC,IAAI;YACf,KAAK,MAAM;gBACT,IAAI,GAAG,CAAC,CAAC,CAAA;;YAEX,KAAK,OAAO;gBACV,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAA;gBACxE,MAAK;YAEP,KAAK,KAAK;gBACR,IAAI,GAAG,CAAC,CAAC,CAAA;;YAEX,KAAK,QAAQ;gBACX,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,YAAY,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAA;gBACtE,MAAK;SACR;KACF;;IAGO,sCAAe,GAAvB,UAAwB,IAAY,EAAE,GAAW;QACzC,IAAA,aAAa,GAAK,IAAI,cAAT,CAAS;QAC5B,IAAI,QAAQ,GAAgB,IAAI,CAAA;QAEhC,KAAwB,UAAkB,EAAlB,KAAA,IAAI,CAAC,YAAa,EAAlB,cAAkB,EAAlB,IAAkB,EAAE;YAAvC,IAAI,WAAW,SAAA;YAClB,IAAI,IAAI,GAAG,WAAW,CAAC,UAAU,CAAA;YACjC,IAAI,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;YAC/B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;YACjC,IAAI,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;YAC5B,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,GAAG,CAAA;;YAGlC,IAAI,QAAQ,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,EAAE;gBACtE,IACE,OAAO,IAAI,aAAa,IAAI,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,WAAW,EAAE;qBACxE,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC,EAC1C;oBACA,QAAQ,GAAG,EAAE,WAAW,aAAA,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAA;iBAC3D;gBAED,IACE,UAAU,IAAI,aAAa,IAAI,IAAI,CAAC,aAAa,IAAI,WAAW,CAAC,aAAa,EAAE;qBAC/E,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,GAAG,UAAU,CAAC,EAC7C;oBACA,QAAQ,GAAG,EAAE,WAAW,aAAA,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAA;iBACjE;gBAED,IACE,QAAQ,IAAI,aAAa,IAAI,IAAI,CAAC,aAAa,IAAI,WAAW,CAAC,aAAa,EAAE;qBAC7E,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,EAC3C;oBACA,QAAQ,GAAG,EAAE,WAAW,aAAA,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAA;iBAC7D;gBAED,IACE,SAAS,IAAI,aAAa,IAAI,IAAI,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,EAAE;qBAChF,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,GAAG,SAAS,CAAC,EAC5C;oBACA,QAAQ,GAAG,EAAE,WAAW,aAAA,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAA;iBAC/D;aACF;SACF;QAED,OAAO,QAAQ,CAAA;KAChB;IAEO,kCAAW,GAAnB,UAAoB,aAA0B;QAC5C,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,UAAC,EAAE;YAC/C,IAAI,EAAE,KAAK,MAAM,EAAE;gBACjB,OAAO,IAAI,qBAAqB,CAAC,KAAK,CAAC,CAAA;aACxC;YACD,OAAO,IAAI,sBAAsB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;SAC7C,CAAC,CAAA;KACH;IAEO,qCAAc,GAAtB,UAAuB,aAA0B;QAC/C,IAAI,GAAG,GAAG,EAAE,CAAA;QAEZ,KAAkB,UAAgB,EAAhB,KAAA,IAAI,CAAC,WAAW,EAAhB,cAAgB,EAAhB,IAAgB,EAAE;YAA/B,IAAI,KAAK,SAAA;YACZ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aAChB;iBAAM;gBACL,GAAG,CAAC,IAAI,OAAR,GAAG,EAAS,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CACpC,SAAS,CAAC,aAAa,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,CACjD,EAAC;aACH;SACF;QAED,OAAO,GAAG,CAAA;KACX;IACH,mBAAC;AAAD,CAAC;;AC5MD;;;;;;;IAM+C,6CAAe;IAkB5D,mCAAoB,WAAwB,EAAE,QAAiB;QAA/D,YACE,kBAAM,WAAW,CAAC,SAanB;QAdmB,iBAAW,GAAX,WAAW,CAAa;;;QAX5C,WAAK,GAAkB,IAAI,CAAA;QAC3B,iBAAW,GAAW,CAAC,CAAA;QACvB,wBAAkB,GAAY,IAAI,CAAA;QAElC,uBAAiB,GAAY,KAAK,CAAA;QAClC,mBAAa,GAAY,KAAK,CAAA;QAC9B,gBAAU,GAAY,KAAK,CAAA;QAC3B,kBAAY,GAAY,KAAK,CAAA;QAC7B,yBAAmB,GAAY,KAAK,CAAA;QACpC,oBAAc,GAAkB,IAAI,CAAA;QA0BpC,mBAAa,GAAG,UAAC,EAAoB;YACnC,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE;gBACpB,KAAI,CAAC,aAAa,GAAG,IAAI,CAAA;gBACzB,KAAI,CAAC,YAAY,GAAG,KAAK,CAAA;gBACzB,KAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;gBAEhC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBAC/B,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;;;;gBAKjC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;oBACf,EAAE,CAAC,SAAS,CAAC,cAAc,EAAE,CAAA;iBAC9B;gBAED,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;gBAEvC,IACE,KAAI,CAAC,aAAa;oBAClB,CAAC,KAAI,CAAC,OAAO,CAAC,gBAAgB,EAC9B;;oBAGA,KAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;oBAC/B,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,SAAwB,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;oBAElE,KAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;oBAEnB,IAAI,CAAC,KAAI,CAAC,WAAW,EAAE;wBACrB,KAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAA;qBACjC;iBACF;aACF;SACF,CAAA;QAED,mBAAa,GAAG,UAAC,EAAoB;YACnC,IAAI,KAAI,CAAC,aAAa,EAAE;gBACtB,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;gBAEvC,IAAI,CAAC,KAAI,CAAC,mBAAmB,EAAE;oBAC7B,IAAI,WAAW,GAAG,KAAI,CAAC,WAAW,CAAA;oBAClC,IAAI,UAAU,SAAA,CAAA;oBACR,IAAA,MAAM,GAAa,EAAE,OAAf,EAAE,MAAM,GAAK,EAAE,OAAP,CAAO;oBAE3B,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAA;oBAC9C,IAAI,UAAU,IAAI,WAAW,GAAG,WAAW,EAAE;wBAC3C,KAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAA;qBACjC;iBACF;gBAED,IAAI,KAAI,CAAC,UAAU,EAAE;;oBAEnB,IAAI,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,QAAQ,EAAE;wBAClC,KAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;wBAC1C,KAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;qBACjD;oBAED,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;iBACrC;aACF;SACF,CAAA;QAED,iBAAW,GAAG,UAAC,EAAoB;YACjC,IAAI,KAAI,CAAC,aAAa,EAAE;gBACtB,KAAI,CAAC,aAAa,GAAG,KAAK,CAAA;gBAE1B,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBAC7B,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBAE/B,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;gBAErC,IAAI,KAAI,CAAC,UAAU,EAAE;oBACnB,KAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;oBACxB,KAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;iBACrB;gBAED,IAAI,KAAI,CAAC,cAAc,EAAE;oBACvB,YAAY,CAAC,KAAI,CAAC,cAAc,CAAC,CAAA;oBACjC,KAAI,CAAC,cAAc,GAAG,IAAI,CAAA;iBAC3B;aACF;SACF,CAAA;QAvGC,IAAI,OAAO,GAAG,KAAI,CAAC,OAAO,GAAG,IAAI,eAAe,CAAC,WAAW,CAAC,CAAA;QAC7D,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAI,CAAC,aAAa,CAAC,CAAA;QACrD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAI,CAAC,aAAa,CAAC,CAAA;QACrD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,WAAW,CAAC,CAAA;QAEjD,IAAI,QAAQ,EAAE;YACZ,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;SAC5B;QAED,KAAI,CAAC,MAAM,GAAG,IAAI,aAAa,EAAE,CAAA;QACjC,KAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAA;;KACvC;IAED,2CAAO,GAAP;QACE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;;;QAItB,IAAI,CAAC,WAAW,CAAC,EAAS,CAAC,CAAA;KAC5B;IAsFD,8CAAU,GAAV,UAAW,EAAoB;QAA/B,iBASC;QARC,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;YAClC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;gBAC/B,KAAI,CAAC,cAAc,GAAG,IAAI,CAAA;gBAC1B,KAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;aACxB,EAAE,IAAI,CAAC,KAAK,CAAQ,CAAA;SACtB;aAAM;YACL,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;SACxB;KACF;IAED,kDAAc,GAAd,UAAe,EAAoB;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QACxB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;KACtB;IAED,2DAAuB,GAAvB,UAAwB,EAAoB;QAC1C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;KACtB;IAED,gDAAY,GAAZ,UAAa,EAAoB;QAC/B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;gBACtB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;gBAE9B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;gBAC7D,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;gBAErC,IAAI,IAAI,CAAC,kBAAkB,KAAK,KAAK,EAAE;oBACrC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;iBACjC;aACF;SACF;KACF;IAED,+CAAW,GAAX,UAAY,EAAoB;;;QAG9B,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAC7B,CAAA;KACF;IAED,4CAAQ,GAAR,UAAS,EAAoB;QAC3B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;KACpC;;IAID,iDAAa,GAAb,UAAc,IAAa;QACzB,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAA;KACrC;IAED,sDAAkB,GAAlB,UAAmB,IAAa;QAC9B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;KAC/B;IAED,wDAAoB,GAApB,UAAqB,IAAa;QAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAA;KAC9B;IAED,wDAAoB,GAApB,UAAqB,IAAa;QAChC,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAA;KACnC;IACH,gCAAC;AAAD,CAlMA,CAA+C,eAAe;;ACZ9D;;;;;;;;AAQA;IAIE,uBAAY,EAAe;QACzB,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,EAAE,CAAC,CAAA;;QAG/B,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC,EAAE,CAAC,CAAC,GAAG,CAC5C,UAAC,QAAQ,IAAK,OAAA,IAAI,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAA,CACzD,CAAA;KACF;IAED,+BAAO,GAAP;QACE,KAAwB,UAAiB,EAAjB,KAAA,IAAI,CAAC,YAAY,EAAjB,cAAiB,EAAjB,IAAiB,EAAE;YAAtC,IAAI,WAAW,SAAA;YAClB,WAAW,CAAC,OAAO,EAAE,CAAA;SACtB;KACF;IAED,mCAAW,GAAX;QACE,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAA;QAE7B,KAAwB,UAAiB,EAAjB,KAAA,IAAI,CAAC,YAAY,EAAjB,cAAiB,EAAjB,IAAiB,EAAE;YAAtC,IAAI,WAAW,SAAA;YAClB,IAAI,IAAI,WAAW,CAAC,cAAc,GAAG,WAAW,CAAC,aAAa,EAAE,CAAA;SACjE;QAED,OAAO,IAAI,CAAA;KACZ;IAED,kCAAU,GAAV;QACE,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAA;QAE3B,KAAwB,UAAiB,EAAjB,KAAA,IAAI,CAAC,YAAY,EAAjB,cAAiB,EAAjB,IAAiB,EAAE;YAAtC,IAAI,WAAW,SAAA;YAClB,GAAG,IAAI,WAAW,CAAC,aAAa,GAAG,WAAW,CAAC,YAAY,EAAE,CAAA;SAC9D;QAED,OAAO,GAAG,CAAA;KACX;IAED,wCAAgB,GAAhB,UAAiB,KAAa,EAAE,KAAa;QAC3C,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAA;QAEvC,KAAwB,UAAiB,EAAjB,KAAA,IAAI,CAAC,YAAY,EAAjB,cAAiB,EAAjB,IAAiB,EAAE;YAAtC,IAAI,WAAW,SAAA;YAClB,IACE,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;gBAChD,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,UAAU,CAAC,EAC/C;gBACA,OAAO,KAAK,CAAA;aACb;SACF;QAED,OAAO,IAAI,CAAA;KACZ;IACH,oBAAC;AAAD,CAAC,IAAA;AAED;AACA;AACA,SAAS,iBAAiB,CAAC,IAAiB;IAC1C,IAAI,OAAO,GAAI,IAAoB,CAAC,OAAO,CAAA;IAE3C,OAAO,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,CAAA;AACjD;;AC9DA;;;;;;;;;;;;;AAaA;IAgBE,qBAAY,QAAyB,EAAE,cAAwC;QAA/E,iBAWC;;QArBD,qBAAgB,GAAY,KAAK,CAAA;QACjC,mBAAc,GAAY,IAAI,CAAA;QAI9B,eAAU,GAAe,IAAI,CAAA;QAC7B,cAAS,GAAe,IAAI,CAAA;QAC5B,aAAQ,GAAe,IAAI,CAAA;QAgB3B,sBAAiB,GAAG,UAAC,EAAoB;YACjC,IAAA,QAAQ,GAAK,KAAI,SAAT,CAAS;YAEvB,KAAI,CAAC,UAAU,GAAG,IAAI,CAAA;YACtB,KAAI,CAAC,SAAS,GAAG,IAAI,CAAA;YACrB,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;YAEpB,KAAI,CAAC,WAAW,EAAE,CAAA;YAClB,KAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAA;YAE1B,IAAI,KAAI,CAAC,UAAU,IAAI,CAAC,KAAI,CAAC,cAAc,EAAE;gBAC3C,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;;gBAG7B,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;aACxC;iBAAM;gBACL,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;aAC7B;SACF,CAAA;QA8BD,oBAAe,GAAG,UAAC,EAAoB;YACrC,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;YACrC,KAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;SAC1B,CAAA;QAED,mBAAc,GAAG,UAAC,EAAoB;YACpC,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;YACpC,KAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;SACpB,CAAA;QAED,oBAAe,GAAG,UAAC,EAAoB;YACrC,KAAI,CAAC,WAAW,EAAE,CAAA;YAClB,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;SACtC,CAAA;QAED,kBAAa,GAAG,UAAC,EAAoB;YACnC,IAAI,KAAI,CAAC,SAAS,EAAE;gBAClB,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;aAClD;YAED,KAAI,CAAC,QAAQ,GAAG,KAAI,CAAC,SAAS,CAAA;YAC9B,KAAI,CAAC,SAAS,GAAG,IAAI,CAAA;YACrB,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;SACpC,CAAA;QAnFC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA;QAEpC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAC1D,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACtD,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAA;QACpD,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACtD,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;QAElD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,EAAE,CAAA;KAC7B;;;IAwBD,uCAAiB,GAAjB,UAAkB,EAAoB;QACpC,IAAI,SAAS,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,KAAK,EAAE,CAAA;QACjD,IAAI,aAAa,GAAG,SAAS,CAAA;QAC7B,IAAI,SAAS,GAAG,EAAE,CAAC,SAAS,CAAA;QAC5B,IAAI,WAAW,CAAA;QAEf,IAAI,SAAS,YAAY,WAAW,EAAE;YACpC,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,CAAA;YACpC,aAAa,GAAG,cAAc,CAAC,aAAa,EAAE,WAAW,CAAC,CAAA;SAC3D;QAED,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,CAAA;QAChG,IAAI,UAAU,EAAE;YACd,IAAI,IAAI,CAAC,gBAAgB,IAAI,WAAW,EAAE;gBACxC,IAAI,iBAAiB,GAAG,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC,CAAA;gBACpE,IAAI,iBAAiB,EAAE;oBACrB,aAAa,GAAG,aAAa,CAAC,iBAAiB,CAAC,CAAA;iBACjD;aACF;YAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;SACxD;aAAM;YACL,IAAI,CAAC,WAAW,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAA;SACvC;KACF;IA2BD,gCAAU,GAAV,UAAW,EAAoB,EAAE,WAAqB;QACpD,IAAI,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAC9B,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,WAAY,CAAC,IAAI,EACjC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,WAAY,CAAC,GAAG,CACjC,CAAA;QAED,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,EAAE;YACpD,IAAI,CAAC,SAAS,GAAG,GAAG,CAAA;YACpB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;SAClD;KACF;IAED,iCAAW,GAAX;QACE,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,UAAC,mBAAmB;YACrE,mBAAmB,CAAC,SAAS,CAAC,WAAW,EAAE,CAAA;YAC3C,OAAO,IAAI,aAAa,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAA;SACjD,CAAC,CAAA;KACH;IAED,iCAAW,GAAX;QACQ,IAAA,cAAc,GAAK,IAAI,eAAT,CAAS;QAE7B,KAAK,IAAI,EAAE,IAAI,cAAc,EAAE;YAC7B,cAAc,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAA;SAC7B;QAED,IAAI,CAAC,cAAc,GAAG,EAAE,CAAA;KACzB;IAED,uCAAiB,GAAjB,UAAkB,UAAkB,EAAE,SAAiB;QACjD,IAAA,KAAqC,IAAI,EAAvC,cAAc,oBAAA,EAAE,cAAc,oBAAS,CAAA;QAC7C,IAAI,OAAO,GAAe,IAAI,CAAA;QAE9B,KAAK,IAAI,EAAE,IAAI,cAAc,EAAE;YAC7B,IAAI,SAAS,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC,SAAS,CAAA;YAC5C,IAAI,aAAa,GAAG,cAAc,CAAC,EAAE,CAAC,CAAA;YAEtC,IACE,aAAa;gBACb,aAAa,CAAC,gBAAgB,CAAC,UAAU,EAAE,SAAS,CAAC,EACrD;gBACA,IAAI,UAAU,GAAG,aAAa,CAAC,WAAW,EAAE,CAAA;gBAC5C,IAAI,SAAS,GAAG,aAAa,CAAC,UAAU,EAAE,CAAA;gBAC1C,IAAI,YAAY,GAAG,UAAU,GAAG,UAAU,CAAA;gBAC1C,IAAI,WAAW,GAAG,SAAS,GAAG,SAAS,CAAA;gBACjC,IAAA,QAAQ,GAAK,aAAa,SAAlB,CAAkB;gBAChC,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAA;gBAC1C,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAA;gBAE3C;;gBAEE,YAAY,IAAI,CAAC,IAAI,YAAY,GAAG,KAAK;oBACzC,WAAW,IAAI,CAAC,IAAI,WAAW,GAAG,MAAM,EACxC;oBACA,IAAI,GAAG,GAAG,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;oBACtE,IACE,GAAG;;oBAED,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CACpE;yBACA,CAAC,OAAO,IAAI,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,EACvC;wBACA,GAAG,CAAC,WAAW,GAAG,EAAE,CAAA;wBACpB,GAAG,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAA;;wBAG/B,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,CAAA;wBAC3B,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,UAAU,CAAA;wBAC5B,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,SAAS,CAAA;wBACzB,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,SAAS,CAAA;wBAE5B,OAAO,GAAG,GAAG,CAAA;qBACd;iBACF;aACF;SACF;QAED,OAAO,OAAO,CAAA;KACf;IACH,kBAAC;AAAD,CAAC,IAAA;SAEe,WAAW,CAAC,IAAgB,EAAE,IAAgB;IAC5D,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE;QAClB,OAAO,IAAI,CAAA;KACZ;IAED,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE;QACnC,OAAO,KAAK,CAAA;KACb;IAED,OAAO,gBAAgB,CAAC,IAAK,CAAC,QAAQ,EAAE,IAAK,CAAC,QAAQ,CAAC,CAAA;AACzD;;SCxMgB,4BAA4B,CAAC,QAAkB,EAAE,OAAwB;IACvF,IAAI,KAAK,GAAG,EAAkB,CAAA;IAE9B,KAAsB,UAAuC,EAAvC,KAAA,OAAO,CAAC,WAAW,CAAC,mBAAmB,EAAvC,cAAuC,EAAvC,IAAuC,EAAE;QAA1D,IAAI,SAAS,SAAA;QAChB,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;KAC9C;IAED,QAAQ,CAAC,KAAK,EAAE,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;IAE7D,OAAO,KAAK,CAAA;AACd,CAAC;SAEe,iBAAiB,CAAC,IAAc,EAAE,OAAgB;IAChE,OAAO;QACL,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QACtC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;QACvE,MAAM,EAAE,IAAI,CAAC,MAAM;KACpB,CAAA;AACH;;ACtBA;;;;AAIA;IAAkC,gCAAW;IAI3C,sBAAY,QAA6B;QAAzC,YACE,kBAAM,QAAQ,CAAC,SAShB;QAMD,uBAAiB,GAAG,UAAC,GAAqB;YAClC,IAAA,QAAQ,GAAK,KAAI,SAAT,CAAS;YACvB,IAAI,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,MAAqB,CAAA;;YAGhD,QAAQ,CAAC,aAAa,CACpB,CAAC,KAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAC1C,CAAA;SACF,CAAA;;QAGD,mBAAa,GAAG,UAAC,EAAoB;YAC7B,IAAA,SAAS,GAAK,KAAI,UAAT,CAAS;YAClB,IAAA,OAAO,GAAK,KAAI,CAAC,QAAQ,QAAlB,CAAkB;YAE/B,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;gBACvB,IAAA,KAA2B,KAAI,CAAC,WAAW,EAAzC,UAAU,gBAAA,EAAE,QAAQ,cAAqB,CAAA;gBAE/C,IAAI,UAAU,IAAI,QAAQ,IAAI,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACzD,IAAA,OAAO,GAAK,SAAS,QAAd,CAAc;oBAC3B,IAAI,GAAG,yBACF,4BAA4B,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,KAC7D,KAAK,EAAE,UAAU,CAAC,KAAK,EACvB,OAAO,EAAE,EAAE,CAAC,SAAuB,EACnC,IAAI,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,GAClD,CAAA;oBAED,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAA;iBAC1C;aACF;SACF,CAAA;;QA1CC,KAAI,CAAC,QAAQ,GAAG,IAAI,yBAAyB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC1D,KAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAA;QAE5C,IAAI,WAAW,GAAG,KAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,KAAI,CAAC,QAAQ,EAAE,0BAA0B,CAAC,QAAQ,CAAC,CAAC,CAAA;QACzG,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,CAAA;QAC7D,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAI,CAAC,aAAa,CAAC,CAAA;;KACtD;IAED,8BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;KACxB;IAiCH,mBAAC;AAAD,CAnDA,CAAkC,WAAW;;ACT7C;;;;AAIA;IAAmC,iCAAW;IAK5C,uBAAY,QAA6B;QAAzC,YACE,kBAAM,QAAQ,CAAC,SAchB;QAjBD,mBAAa,GAAoB,IAAI,CAAA;QAuBrC,uBAAiB,GAAG,UAAC,EAAoB;YACnC,IAAA,KAA0B,KAAI,EAA5B,SAAS,eAAA,EAAE,QAAQ,cAAS,CAAA;YAC5B,IAAA,OAAO,GAAK,SAAS,CAAC,OAAO,QAAtB,CAAsB;YAEnC,IAAI,SAAS,GAAG,OAAO,CAAC,UAAU;gBAChC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC,SAAS,CAAC,MAAqB,CAAC,CAAA;;YAGjE,QAAQ,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,CAAA;;YAGlC,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC,OAAO,GAAGA,wBAAsB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;SACvE,CAAA;QAED,qBAAe,GAAG,UAAC,EAAoB;YACrC,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;SAChD,CAAA;QAED,qBAAe,GAAG,UAAC,GAAe,EAAE,OAAgB;YAC5C,IAAA,OAAO,GAAK,KAAI,CAAC,SAAS,QAAnB,CAAmB;YAChC,IAAI,aAAa,GAAoB,IAAI,CAAA;YACzC,IAAI,SAAS,GAAG,KAAK,CAAA;YAErB,IAAI,GAAG,EAAE;gBACP,IAAI,UAAU,GAAG,KAAI,CAAC,WAAW,CAAC,UAAW,CAAA;gBAC7C,IAAI,UAAU,GAAG,GAAG,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW;uBACtD,KAAI,CAAC,iBAAiB;uBACtB,CAAC,KAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;gBAE7C,IAAI,CAAC,UAAU,EAAE;oBACf,aAAa,GAAG,qBAAqB,CACnC,UAAU,EACV,GAAG,EACH,OAAO,CAAC,WAAW,CAAC,yBAAyB,CAC9C,CAAA;iBACF;gBAED,IAAI,CAAC,aAAa,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;oBACpF,SAAS,GAAG,IAAI,CAAA;oBAChB,aAAa,GAAG,IAAI,CAAA;iBACrB;aACF;YAED,IAAI,aAAa,EAAE;gBACjB,OAAO,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,CAAA;aACrE;iBAAM,IAAI,CAAC,OAAO,EAAE;gBACnB,OAAO,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAA;aAC7C;YAED,IAAI,CAAC,SAAS,EAAE;gBACd,YAAY,EAAE,CAAA;aACf;iBAAM;gBACL,aAAa,EAAE,CAAA;aAChB;YAED,IAAI,CAAC,OAAO,EAAE;gBACZ,KAAI,CAAC,aAAa,GAAG,aAAa,CAAA;aACnC;SACF,CAAA;QAED,qBAAe,GAAG,UAAC,GAAqB;YACtC,IAAI,KAAI,CAAC,aAAa,EAAE;;gBAEtB,iBAAiB,CAAC,KAAI,CAAC,aAAa,EAAE,GAAG,EAAE,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;gBAElE,KAAI,CAAC,aAAa,GAAG,IAAI,CAAA;aAC1B;SACF,CAAA;QAtFO,IAAA,SAAS,GAAK,QAAQ,UAAb,CAAa;QACtB,IAAA,OAAO,GAAK,SAAS,CAAC,OAAO,QAAtB,CAAsB;QAEnC,IAAI,QAAQ,GAAG,KAAI,CAAC,QAAQ,GAAG,IAAI,yBAAyB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QACzE,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAA;QACnC,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,iBAAiB,IAAI,CAAC,CAAA;QACrD,QAAQ,CAAC,YAAY,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAA;QAEpD,IAAI,WAAW,GAAG,KAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,KAAI,CAAC,QAAQ,EAAE,0BAA0B,CAAC,QAAQ,CAAC,CAAC,CAAA;QACzG,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,CAAA;QAC7D,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;;KAC1D;IAED,+BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;KACxB;IAsEH,oBAAC;AAAD,CA9FA,CAAmC,WAAW,GA8F7C;AAED,SAASA,wBAAsB,CAAC,SAA6B;IACrD,IAAA,OAAO,GAAK,SAAS,CAAC,OAAO,QAAtB,CAAsB;IACnC,IAAI,KAAK,GAAG,OAAO,CAAC,oBAAoB,CAAA;IAExC,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,KAAK,GAAG,OAAO,CAAC,cAAc,CAAA;KAC/B;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,qBAAqB,CAAC,IAAS,EAAE,IAAS,EAAE,yBAAyD;IAC5G,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAA;IAC7B,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAA;IAC7B,IAAI,EAAE,GAAG;QACP,SAAS,CAAC,KAAK,CAAC,KAAK;QACrB,SAAS,CAAC,KAAK,CAAC,GAAG;QACnB,SAAS,CAAC,KAAK,CAAC,KAAK;QACrB,SAAS,CAAC,KAAK,CAAC,GAAG;KACpB,CAAA;IAED,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IAEvB,IAAI,KAAK,GAAG,EAAc,CAAA;IAE1B,KAAwB,UAAyB,EAAzB,uDAAyB,EAAzB,uCAAyB,EAAzB,IAAyB,EAAE;QAA9C,IAAI,WAAW,kCAAA;QAClB,IAAI,GAAG,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAEjC,IAAI,GAAG,KAAK,KAAK,EAAE;YACjB,OAAO,IAAI,CAAA;SACZ;QAED,IAAI,GAAG,EAAE;YACP,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;SACrB;KACF;IAED,KAAK,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;IAC1C,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAA;IAE/B,OAAO,KAAK,CAAA;AACd;;ACjHA;IAAmC,iCAAW;IAkB5C,uBAAY,QAA6B;QAAzC,YACE,kBAAM,QAAQ,CAAC,SAgBhB;;QA1BD,eAAS,GAAuB,IAAI,CAAA;QACpC,gBAAU,GAAe,IAAI,CAAA;QAC7B,gBAAU,GAAY,KAAK,CAAA;QAC3B,gBAAU,GAA4B,IAAI,CAAA;QAC1C,oBAAc,GAAsB,IAAI,CAAA;QACxC,sBAAgB,GAA2B,IAAI,CAAA;QAC/C,mBAAa,GAAyB,IAAI,CAAA;QAC1C,2BAAqB,GAAsB,IAAI,CAAA;QAyB/C,uBAAiB,GAAG,UAAC,EAAoB;YACvC,IAAI,UAAU,GAAG,EAAE,CAAC,SAAS,CAAC,MAAqB,CAAA;YAC/C,IAAA,KAA0B,KAAI,EAA5B,SAAS,eAAA,EAAE,QAAQ,cAAS,CAAA;YAC5B,IAAA,MAAM,GAAK,QAAQ,OAAb,CAAa;YACnB,IAAA,OAAO,GAAK,SAAS,CAAC,OAAO,QAAtB,CAAsB;YACnC,IAAI,cAAc,GAAG,SAAS,CAAC,OAAO,CAAA;YACtC,KAAI,CAAC,SAAS,GAAG,EAAE,CAAC,SAAwB,CAAA;YAC5C,IAAI,UAAU,GAAG,KAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,EAAE,CAAC,SAAwB,CAAE,CAAA;YACzE,IAAI,UAAU,GAAG,KAAI,CAAC,UAAU,GAAG,UAAU,CAAC,UAAW,CAAA;YACzD,IAAI,eAAe,GAAG,UAAU,CAAC,QAAS,CAAC,UAAU,CAAA;YAErD,KAAI,CAAC,cAAc,GAAG,iBAAiB,CACrC,cAAc,CAAC,cAAc,EAAE,CAAC,UAAU,EAC1C,eAAe,CAChB,CAAA;YAED,QAAQ,CAAC,WAAW,GAAG,EAAE,CAAC,OAAO,GAAG,CAAC,GAAG,OAAO,CAAC,oBAAoB,CAAA;YACpE,QAAQ,CAAC,KAAK;;gBAEZ,CAAC,EAAE,CAAC,OAAO,IAAI,eAAe,KAAK,SAAS,CAAC,KAAK,CAAC,cAAc;oBAC/D,sBAAsB,CAAC,SAAS,CAAC;oBACjC,IAAI,CAAA;YAER,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC7B,MAAM,CAAC,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAA;aAC9C;iBAAM;gBACL,MAAM,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;aACtD;YAED,MAAM,CAAC,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAA;YAElD,IAAI,OAAO,GACT,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC;gBACtC,CAAC,cAAc,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAA;YAElD,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAA;;;YAIhC,KAAI,CAAC,UAAU,GAAG,OAAO;gBACtB,EAAE,CAAC,SAAyB,CAAC,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAA;SACzE,CAAA;QAED,qBAAe,GAAG,UAAC,EAAoB;YACrC,IAAI,cAAc,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAA;YAC3C,IAAI,UAAU,GAAG,KAAI,CAAC,UAAW,CAAA;YACjC,IAAI,eAAe,GAAG,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAA;YAEpD,IAAI,EAAE,CAAC,OAAO,EAAE;;gBAEd,IAAI,eAAe,KAAK,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,EAAE;oBAC3D,cAAc,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,eAAe,iBAAA,EAAE,CAAC,CAAA;iBACnE;aACF;iBAAM;;gBAEL,cAAc,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAA;aACpD;YAED,IAAI,KAAI,CAAC,UAAU,EAAE;gBACnB,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;gBACvC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE;oBAC/C,EAAE,EAAE,KAAI,CAAC,SAAS;oBAClB,KAAK,EAAE,IAAI,QAAQ,CAAC,cAAc,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC;oBACxE,OAAO,EAAE,EAAE,CAAC,SAAuB;oBACnC,IAAI,EAAE,cAAc,CAAC,OAAO;iBACR,CAAC,CAAA;aACxB;SACF,CAAA;QAED,qBAAe,GAAG,UAAC,GAAe,EAAE,OAAgB;YAClD,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE;gBACpB,OAAM;aACP;YAED,IAAI,cAAc,GAAG,KAAI,CAAC,cAAe,CAAA;YACzC,IAAI,UAAU,GAAG,KAAI,CAAC,WAAW,CAAC,UAAW,CAAA;YAC7C,IAAI,cAAc,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAA;;YAG3C,IAAI,gBAAgB,GAA2B,IAAI,CAAA;YACnD,IAAI,QAAQ,GAAyB,IAAI,CAAA;YACzC,IAAI,qBAAqB,GAAsB,IAAI,CAAA;YACnD,IAAI,SAAS,GAAG,KAAK,CAAA;YACrB,IAAI,WAAW,GAA0B;gBACvC,cAAc,EAAE,cAAc;gBAC9B,aAAa,EAAE,qBAAqB,EAAE;gBACtC,OAAO,EAAE,IAAI;aACd,CAAA;YAED,IAAI,GAAG,EAAE;gBACP,gBAAgB,GAAG,GAAG,CAAC,OAAO,CAAA;gBAC9B,IAAI,gBAAgB,GAAG,gBAAgB,CAAC,OAAO,CAAA;gBAE/C,IACE,cAAc,KAAK,gBAAgB;qBAClC,gBAAgB,CAAC,QAAQ,IAAI,gBAAgB,CAAC,SAAS,CAAC,EACzD;oBACA,QAAQ,GAAG,oBAAoB,CAAC,UAAU,EAAE,GAAG,EAAE,gBAAgB,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,0BAA0B,CAAC,CAAA;oBAE1H,IAAI,QAAQ,EAAE;wBACZ,qBAAqB,GAAG,yBAAyB,CAC/C,cAAc,EACd,gBAAgB,CAAC,cAAc,EAAE,CAAC,YAAY,EAC9C,QAAQ,EACR,gBAAgB,CACjB,CAAA;wBACD,WAAW,CAAC,aAAa,GAAG,qBAAqB,CAAA;wBAEjD,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,gBAAgB,CAAC,EAAE;4BACvE,SAAS,GAAG,IAAI,CAAA;4BAChB,QAAQ,GAAG,IAAI,CAAA;4BACf,qBAAqB,GAAG,IAAI,CAAA;4BAC5B,WAAW,CAAC,aAAa,GAAG,qBAAqB,EAAE,CAAA;yBACpD;qBACF;iBACF;qBAAM;oBACL,gBAAgB,GAAG,IAAI,CAAA;iBACxB;aACF;YAED,KAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAA;YAE/C,IAAI,CAAC,SAAS,EAAE;gBACd,YAAY,EAAE,CAAA;aACf;iBAAM;gBACL,aAAa,EAAE,CAAA;aAChB;YAED,IAAI,CAAC,OAAO,EAAE;gBACZ,IACE,cAAc,KAAK,gBAAgB;oBACnC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAC5B;oBACA,QAAQ,GAAG,IAAI,CAAA;iBAChB;gBAED,KAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAA;;;gBAI7C,KAAI,CAAC,QAAQ,CAAC,kBAAkB,CAC9B,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,kBAAkB,CAAC,CACrE,CAAA;;gBAGD,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;gBACxC,KAAI,CAAC,aAAa,GAAG,QAAQ,CAAA;gBAC7B,KAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAA;aACnD;SACF,CAAA;QAED,qBAAe,GAAG;YAChB,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE;gBACpB,KAAI,CAAC,OAAO,EAAE,CAAA;aACf;SACF,CAAA;QAED,mBAAa,GAAG,UAAC,EAAoB;YACnC,IAAI,KAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,gBAAc,GAAG,KAAI,CAAC,SAAS,CAAC,OAAO,CAAA;gBAC3C,IAAI,WAAW,GAAG,gBAAc,CAAC,OAAO,CAAA;gBACpC,IAAA,KAAsC,KAAI,EAAxC,kBAAgB,sBAAA,EAAE,aAAa,mBAAS,CAAA;gBAC9C,IAAI,QAAQ,GAAG,KAAI,CAAC,UAAW,CAAC,GAAG,CAAA;gBACnC,IAAI,aAAa,GAAG,KAAI,CAAC,UAAW,CAAC,QAAQ,CAAA;gBAC7C,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,gBAAc,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAA;gBACpE,IAAI,gBAAc,GAAG,KAAI,CAAC,cAAe,CAAA;gBACzC,IAAI,uBAAqB,GAAG,KAAI,CAAC,qBAAsB,CAAA;gBACjD,IAAA,QAAQ,GAAK,KAAI,CAAC,WAAW,SAArB,CAAqB;gBAEnC,KAAI,CAAC,SAAS,EAAE,CAAA;gBAEhB,gBAAc,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE;oBAC9C,EAAE,EAAE,KAAI,CAAC,SAAS;oBAClB,KAAK,EAAE,QAAQ;oBACf,OAAO,EAAE,EAAE,CAAC,SAAuB;oBACnC,IAAI,EAAE,WAAW;iBACE,CAAC,CAAA;gBAEtB,IAAI,aAAa,EAAE;;oBAEjB,IAAI,kBAAgB,KAAK,gBAAc,EAAE;wBACvC,IAAI,eAAe,GAAG,IAAI,QAAQ,CAChC,gBAAc,EACd,uBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAC1C,aAAa,GAAG,uBAAqB,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,CACjF,CAAA;wBAED,gBAAc,CAAC,QAAQ,CAAC;4BACtB,IAAI,EAAE,cAAc;4BACpB,UAAU,EAAE,uBAAqB;yBAClC,CAAC,CAAA;wBAEF,IAAI,cAAc,GAAmB;4BACnC,QAAQ,EAAE,QAAQ;4BAClB,KAAK,EAAE,eAAe;4BACtB,aAAa,EAAE,cAAc,CAAC,uBAAqB,EAAE,gBAAc,EAAE,aAAa,CAAC;4BACnF,MAAM;gCACJ,gBAAc,CAAC,QAAQ,CAAC;oCACtB,IAAI,EAAE,cAAc;oCACpB,UAAU,EAAE,gBAAc;iCAC3B,CAAC,CAAA;6BACH;yBACF,CAAA;wBAED,IAAI,WAAW,GAAsC,EAAE,CAAA;wBACvD,KAAwB,UAAiE,EAAjE,KAAA,gBAAc,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,qBAAqB,EAAjE,cAAiE,EAAjE,IAAiE,EAAE;4BAAtF,IAAI,WAAW,SAAA;4BAClB,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC,aAAa,EAAE,gBAAc,CAAC,CAAC,CAAA;yBAClE;wBAED,gBAAc,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,iCACrC,cAAc,GACd,WAAW,KACd,EAAE,EAAE,EAAE,CAAC,SAAwB,EAC/B,KAAK,EAAE,aAAa,CAAC,UAAW,EAChC,OAAO,EAAE,EAAE,CAAC,SAAuB,EACnC,IAAI,EAAE,WAAW,IACjB,CAAA;wBAEF,gBAAc,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAA;;qBAG9D;yBAAM,IAAI,kBAAgB,EAAE;wBAC3B,IAAI,cAAc,GAAmB;4BACnC,KAAK,EAAE,QAAQ;4BACf,aAAa,EAAE,cAAc,CAAC,gBAAc,EAAE,gBAAc,EAAE,aAAa,CAAC;4BAC5E,MAAM;gCACJ,gBAAc,CAAC,QAAQ,CAAC;oCACtB,IAAI,EAAE,cAAc;oCACpB,UAAU,EAAE,gBAAc;iCAC3B,CAAC,CAAA;6BACH;yBACF,CAAA;wBAED,gBAAc,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,wBACtC,cAAc,KACjB,SAAS,EAAE,EAAE,CAAC,SAAwB,EACtC,IAAI,EAAE,WAAW,IACjB,CAAA;wBAEF,gBAAc,CAAC,QAAQ,CAAC;4BACtB,IAAI,EAAE,eAAe;4BACrB,UAAU,EAAE,gBAAc;yBAC3B,CAAC,CAAA;wBAEF,gBAAc,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAA;wBAE7D,IAAI,aAAa,GAAG,uBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;wBAC9D,IAAI,kBAAkB,GAAG,uBAAqB,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;wBAClF,IAAI,aAAa,GAAG,IAAI,QAAQ,CAAC,kBAAgB,EAAE,aAAa,EAAE,kBAAkB,CAAC,CAAA;wBAErF,kBAAgB,CAAC,QAAQ,CAAC;4BACxB,IAAI,EAAE,cAAc;4BACpB,UAAU,EAAE,uBAAqB;yBAClC,CAAC,CAAA;wBAEF,IAAI,WAAW,GAAgB;4BAC7B,KAAK,EAAE,aAAa;4BACpB,aAAa,EAAE,cAAc,CAAC,uBAAqB,EAAE,kBAAgB,EAAE,kBAAkB,CAAC;4BAC1F,MAAM;gCACJ,kBAAgB,CAAC,QAAQ,CAAC;oCACxB,IAAI,EAAE,eAAe;oCACrB,UAAU,EAAE,uBAAqB;iCAClC,CAAC,CAAA;6BACH;yBACF,CAAA;wBAED,kBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;wBAEzD,IAAI,EAAE,CAAC,OAAO,EAAE;4BACd,kBAAgB,CAAC,QAAQ,CAAC;gCACxB,IAAI,EAAE,cAAc;gCACpB,eAAe,EAAE,aAAa,CAAC,UAAU;6BAC1C,CAAC,CAAA;yBACH;wBAED,kBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,wBAClC,4BAA4B,CAAC,QAAQ,CAAC,QAAQ,EAAE,kBAAgB,CAAC,KACpE,SAAS,EAAE,EAAE,CAAC,SAAwB,EACtC,OAAO,EAAE,EAAE,CAAC,SAAuB,EACnC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,IAC9B,CAAA;wBAEF,kBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,wBAC1C,WAAW,KACd,SAAS,EAAE,EAAE,CAAC,SAAwB,EACtC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,IAC9B,CAAA;qBACH;iBACF;qBAAM;oBACL,gBAAc,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;iBAC/C;aACF;YAED,KAAI,CAAC,OAAO,EAAE,CAAA;SACf,CAAA;QA3TO,IAAA,SAAS,GAAK,KAAI,UAAT,CAAS;QAClB,IAAA,OAAO,GAAK,SAAS,CAAC,OAAO,QAAtB,CAAsB;QAEnC,IAAI,QAAQ,GAAG,KAAI,CAAC,QAAQ,GAAG,IAAI,yBAAyB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QACzE,QAAQ,CAAC,OAAO,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAA;QAClD,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAA;QACnC,QAAQ,CAAC,YAAY,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAA;QAEpD,IAAI,WAAW,GAAG,KAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,KAAI,CAAC,QAAQ,EAAE,wBAAwB,CAAC,CAAA;QAC7F,WAAW,CAAC,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAA;QACtD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,CAAA;QAC7D,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAI,CAAC,aAAa,CAAC,CAAA;;KACtD;IAED,+BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;KACxB;;IA2SD,mCAAW,GAAX,UAAY,WAAmC,EAAE,KAA4B;QAC3E,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAA;QAC3C,IAAI,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAA;;QAGvC,IAAI,WAAW,IAAI,WAAW,KAAK,WAAW,EAAE;;;YAG9C,IAAI,WAAW,KAAK,cAAc,EAAE;gBAClC,WAAW,CAAC,QAAQ,CAAC;oBACnB,IAAI,EAAE,gBAAgB;oBACtB,KAAK,EAAE;wBACL,cAAc,EAAE,KAAK,CAAC,cAAc;wBACpC,aAAa,EAAE,qBAAqB,EAAE;wBACtC,OAAO,EAAE,IAAI;qBACd;iBACF,CAAC,CAAA;;aAGH;iBAAM;gBACL,WAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;aACnD;SACF;QAED,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,OAAA,EAAE,CAAC,CAAA;SACxD;KACF;IAED,iCAAS,GAAT;QACE,IAAI,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAA;QACtC,IAAA,gBAAgB,GAAK,IAAI,iBAAT,CAAS;QAE/B,IAAI,gBAAgB,EAAE;YACpB,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;SACxD;;QAGD,IAAI,eAAe,KAAK,gBAAgB,EAAE;YACxC,eAAe,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;SACvD;KACF;IAED,+BAAO,GAAP;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;QAC1B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAA;KAClC;;;IAlYM,sBAAQ,GAAG,0CAA0C,CAAA;IAmY9D,oBAAC;CAAA,CAtYkC,WAAW,GAsY7C;AAED,SAAS,oBAAoB,CAAC,IAAS,EAAE,IAAS,EAAE,SAAsC;IACxF,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAA;IAC7B,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAA;IAC7B,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAA;IACjC,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAA;IACjC,IAAI,aAAa,GAAG,EAAS,CAAA;IAE7B,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;QACzC,aAAa,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAA;QACvC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAA;QAElE,IAAI,SAAS,CAAC,MAAM,EAAE;;;YAGpB,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;SAC1B;KACF;IAED,IAAI,KAAK,GAAG,SAAS,CACnB,KAAK,EAAE,KAAK,EACZ,IAAI,CAAC,OAAO,CAAC,OAAO,EACpB,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW;QACnC,IAAI,CAAC,SAAS;QACd,IAAI,CACP,CAAA;IAED,IAAI,KAAK,CAAC,YAAY,EAAE;QACtB,aAAa,CAAC,MAAM,GAAG,KAAK,CAAA;KAC7B;IAED,IAAI,QAAQ,GAAkB;QAC5B,UAAU,EAAE,KAAK;QACjB,aAAa,eAAA;KACd,CAAA;IAED,KAAqB,UAAS,EAAT,uBAAS,EAAT,uBAAS,EAAT,IAAS,EAAE;QAA3B,IAAI,QAAQ,kBAAA;QACf,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;KAC/B;IAED,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,SAAS,sBAAsB,CAAC,SAA6B;IACrD,IAAA,OAAO,GAAK,SAAS,CAAC,OAAO,QAAtB,CAAsB;IACnC,IAAI,KAAK,GAAG,OAAO,CAAC,mBAAmB,CAAA;IAEvC,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,KAAK,GAAG,OAAO,CAAC,cAAc,CAAA;KAC/B;IAED,OAAO,KAAK,CAAA;AACd;;AC7bA;IAAmC,iCAAW;IAY5C,uBAAY,QAA6B;QAAzC,YACE,kBAAM,QAAQ,CAAC,SAahB;;QArBD,mBAAa,GAAuB,IAAI,CAAA;QACxC,iBAAW,GAAe,IAAI,CAAA;QAC9B,gBAAU,GAA4B,IAAI,CAAA;QAC1C,oBAAc,GAAsB,IAAI,CAAA;QACxC,mBAAa,GAAyB,IAAI,CAAA;QAC1C,2BAAqB,GAAsB,IAAI,CAAA;QAsB/C,uBAAiB,GAAG,UAAC,EAAoB;YACjC,IAAA,SAAS,GAAK,KAAI,UAAT,CAAS;YACxB,IAAI,KAAK,GAAG,KAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;YAC/B,IAAI,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;YACzB,IAAI,UAAU,GAAG,KAAI,CAAC,UAAU,GAAG,GAAG,CAAC,UAAW,CAAA;YAElD,KAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAA;;YAG1E,KAAI,CAAC,QAAQ,CAAC,aAAa,CACzB,CAAC,KAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC,SAAS,CAAC,MAAqB,CAAC;iBACnE,EAAE,CAAC,OAAO,IAAI,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,KAAK,UAAU,CAAC,QAAS,CAAC,UAAU,CAAC,CACxF,CAAA;SACF,CAAA;QAED,qBAAe,GAAG,UAAC,EAAoB;YAC/B,IAAA,OAAO,GAAK,KAAI,CAAC,SAAS,QAAnB,CAAmB;YAChC,IAAI,UAAU,GAAG,KAAI,CAAC,UAAW,CAAA;YAEjC,KAAI,CAAC,cAAc,GAAG,iBAAiB,CACrC,OAAO,CAAC,cAAc,EAAE,CAAC,UAAU,EACnC,KAAI,CAAC,UAAU,CAAC,QAAS,CAAC,UAAU,CACrC,CAAA;YAED,IAAI,KAAK,GAAG,KAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;YAC/B,KAAI,CAAC,aAAa,GAAG,KAAK,CAAA;YAC1B,KAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;YAElC,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAA;YAC9B,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE;gBAC1C,EAAE,EAAE,KAAK;gBACT,KAAK,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC;gBACjE,OAAO,EAAE,EAAE,CAAC,SAAuB;gBACnC,IAAI,EAAE,OAAO,CAAC,OAAO;aACC,CAAC,CAAA;SAC1B,CAAA;QAED,qBAAe,GAAG,UAAC,GAAe,EAAE,OAAgB,EAAE,EAAoB;YAClE,IAAA,OAAO,GAAK,KAAI,CAAC,SAAS,QAAnB,CAAmB;YAChC,IAAI,cAAc,GAAG,KAAI,CAAC,cAAe,CAAA;YACzC,IAAI,UAAU,GAAG,KAAI,CAAC,WAAW,CAAC,UAAW,CAAA;YAC7C,IAAI,aAAa,GAAG,KAAI,CAAC,UAAU,CAAC,QAAS,CAAA;YAC7C,IAAI,QAAQ,GAAyB,IAAI,CAAA;YACzC,IAAI,qBAAqB,GAAsB,IAAI,CAAA;YACnD,IAAI,SAAS,GAAG,KAAK,CAAA;YACrB,IAAI,WAAW,GAA0B;gBACvC,cAAc,EAAE,cAAc;gBAC9B,aAAa,EAAE,qBAAqB,EAAE;gBACtC,OAAO,EAAE,IAAI;aACd,CAAA;YAED,IAAI,GAAG,EAAE;gBACP,IAAI,UAAU,GAAG,GAAG,CAAC,WAAW,KAAK,UAAU,CAAC,WAAW;uBACtD,KAAI,CAAC,iBAAiB;uBACtB,CAAC,KAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;gBAE7C,IAAI,CAAC,UAAU,EAAE;oBACf,QAAQ,GAAG,eAAe,CACxB,UAAU,EACV,GAAG,EACF,EAAE,CAAC,SAAyB,CAAC,SAAS,CAAC,QAAQ,CAAC,wBAAwB,CAAC,EAC1E,aAAa,CAAC,KAAK,CACpB,CAAA;iBACF;aACF;YAED,IAAI,QAAQ,EAAE;gBACZ,qBAAqB,GAAG,yBAAyB,CAAC,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;gBAC3H,WAAW,CAAC,aAAa,GAAG,qBAAqB,CAAA;gBAEjD,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;oBAC9D,SAAS,GAAG,IAAI,CAAA;oBAChB,QAAQ,GAAG,IAAI,CAAA;oBACf,qBAAqB,GAAG,IAAI,CAAA;oBAC5B,WAAW,CAAC,aAAa,GAAG,IAAI,CAAA;iBACjC;aACF;YAED,IAAI,qBAAqB,EAAE;gBACzB,OAAO,CAAC,QAAQ,CAAC;oBACf,IAAI,EAAE,kBAAkB;oBACxB,KAAK,EAAE,WAAW;iBACnB,CAAC,CAAA;aACH;iBAAM;gBACL,OAAO,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAA;aACjD;YAED,IAAI,CAAC,SAAS,EAAE;gBACd,YAAY,EAAE,CAAA;aACf;iBAAM;gBACL,aAAa,EAAE,CAAA;aAChB;YAED,IAAI,CAAC,OAAO,EAAE;gBACZ,IAAI,QAAQ,IAAI,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE;oBAC5C,QAAQ,GAAG,IAAI,CAAA;iBAChB;gBAED,KAAI,CAAC,aAAa,GAAG,QAAQ,CAAA;gBAC7B,KAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAA;aACnD;SACF,CAAA;QAED,mBAAa,GAAG,UAAC,EAAoB;YAC7B,IAAA,OAAO,GAAK,KAAI,CAAC,SAAS,QAAnB,CAAmB;YAChC,IAAI,QAAQ,GAAG,KAAI,CAAC,UAAW,CAAC,GAAG,CAAA;YACnC,IAAI,aAAa,GAAG,KAAI,CAAC,UAAW,CAAC,QAAQ,CAAA;YAC7C,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAA;YAC7D,IAAI,cAAc,GAAG,KAAI,CAAC,cAAe,CAAA;YACzC,IAAI,qBAAqB,GAAG,KAAI,CAAC,qBAAsB,CAAA;YAEvD,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE;gBACzC,EAAE,EAAE,KAAI,CAAC,aAAa;gBACtB,KAAK,EAAE,QAAQ;gBACf,OAAO,EAAE,EAAE,CAAC,SAAuB;gBACnC,IAAI,EAAE,OAAO,CAAC,OAAO;aACA,CAAC,CAAA;YAExB,IAAI,KAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,eAAe,GAAG,IAAI,QAAQ,CAChC,OAAO,EACP,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAC1C,aAAa,GAAG,qBAAqB,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAI,CACjF,CAAA;gBAED,OAAO,CAAC,QAAQ,CAAC;oBACf,IAAI,EAAE,cAAc;oBACpB,UAAU,EAAE,qBAAqB;iBAClC,CAAC,CAAA;gBAEF,IAAI,cAAc,GAAmB;oBACnC,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,eAAe;oBACtB,aAAa,EAAE,cAAc,CAAC,qBAAqB,EAAE,OAAO,EAAE,aAAa,CAAC;oBAC5E,MAAM;wBACJ,OAAO,CAAC,QAAQ,CAAC;4BACf,IAAI,EAAE,cAAc;4BACpB,UAAU,EAAE,cAAc;yBAC3B,CAAC,CAAA;qBACH;iBACF,CAAA;gBAED,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,wBAChC,cAAc,KACjB,EAAE,EAAE,KAAI,CAAC,aAAa,EACtB,UAAU,EAAE,KAAI,CAAC,aAAa,CAAC,UAAU,IAAI,cAAc,CAAC,CAAC,CAAC,EAC9D,QAAQ,EAAE,KAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,cAAc,CAAC,CAAC,CAAC,EAC1D,OAAO,EAAE,EAAE,CAAC,SAAuB,EACnC,IAAI,EAAE,OAAO,CAAC,OAAO,IACrB,CAAA;gBAEF,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAA;aACvD;iBAAM;gBACL,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;aAC1C;;YAGD,KAAI,CAAC,WAAW,GAAG,IAAI,CAAA;YACvB,KAAI,CAAC,cAAc,GAAG,IAAI,CAAA;YAC1B,KAAI,CAAC,aAAa,GAAG,IAAI,CAAA;;SAG1B,CAAA;QApLO,IAAA,SAAS,GAAK,QAAQ,UAAb,CAAa;QAE5B,IAAI,QAAQ,GAAG,KAAI,CAAC,QAAQ,GAAG,IAAI,yBAAyB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QACzE,QAAQ,CAAC,OAAO,CAAC,QAAQ,GAAG,mBAAmB,CAAA;QAC/C,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAA;QACnC,QAAQ,CAAC,YAAY,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAA;QAEtE,IAAI,WAAW,GAAG,KAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,KAAI,CAAC,QAAQ,EAAE,0BAA0B,CAAC,QAAQ,CAAC,CAAC,CAAA;QACzG,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,CAAA;QAC7D,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAI,CAAC,aAAa,CAAC,CAAA;;KACtD;IAED,+BAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;KACxB;IAsKD,kCAAU,GAAV,UAAW,EAAoB;QAC7B,OAAO,cAAc,CAAC,EAAE,CAAC,SAAwB,EAAE,WAAW,CAAC,CAAA;KAChE;IACH,oBAAC;AAAD,CAvMA,CAAmC,WAAW,GAuM7C;AAED,SAAS,eAAe,CACtB,IAAS,EACT,IAAS,EACT,WAAoB,EACpB,aAAwB;IAExB,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;IAClC,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAA;IACrC,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAA;IAErC,IAAI,KAAK,GAAG,SAAS,CACnB,KAAK,EAAE,KAAK,EACZ,OAAO,EACP,IAAI,CAAC,SAAS,CACf,CAAA;IAED,IAAI,WAAW,EAAE;QACf,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE;YAC/D,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,CAAA;SAC7B;KACF;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE;QACtE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAA;KAC3B;IAED,OAAO,IAAI,CAAA;AACb;;AC5PA;IAME,sBAAoB,OAAwB;QAA5C,iBAWC;QAXmB,YAAO,GAAP,OAAO,CAAiB;QAJ5C,8BAAyB,GAAG,KAAK,CAAA;QACjC,kBAAa,GAAG,KAAK,CAAA;QACrB,iBAAY,GAAG,KAAK,CAAA;QAoBpB,aAAQ,GAAG,UAAC,UAA4B;YACtC,IAAI,UAAU,CAAC,OAAO,EAAE;gBACtB,KAAI,CAAC,yBAAyB,GAAG,IAAI,CAAA;aACtC;SACF,CAAA;QAED,0BAAqB,GAAG,UAAC,GAAqB;YAC5C,IAAI,cAAc,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAA;YACxD,IAAI,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAgB,CAAA;YAEhE,KAAI,CAAC,aAAa,GAAG,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;YAC7D,KAAI,CAAC,YAAY,GAAG,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAA;SACrE,CAAA;QAED,wBAAmB,GAAG,UAAC,GAAqB;YACpC,IAAA,OAAO,GAAK,KAAI,QAAT,CAAS;YAChB,IAAA,eAAe,GAAK,KAAI,gBAAT,CAAS;YAC9B,IAAI,aAAa,GAAG,OAAO,CAAC,cAAc,EAAE,CAAA;;YAG5C,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE;gBACnC,IACE,aAAa,CAAC,aAAa;oBAC3B,CAAC,KAAI,CAAC,yBAAyB;kBAC/B;oBACA,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAA;oBAE/C,IAAI,YAAY,KAAK,CAAC,YAAY,IAAI,CAAC,KAAI,CAAC,aAAa,CAAC,EAAE;wBAC1D,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;qBAClC;iBACF;gBAED,IACE,aAAa,CAAC,cAAc;oBAC5B,CAAC,KAAI,CAAC,YAAY;kBAClB;oBACA,OAAO,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAA;iBAC7C;aACF;YAED,KAAI,CAAC,yBAAyB,GAAG,KAAK,CAAA;SACvC,CAAA;QA1DC,IAAI,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAA;QAC1E,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAA;QACvC,eAAe,CAAC,iBAAiB,GAAG,KAAK,CAAA;QACzC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAA;QACrE,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAA;;;;QAKjE,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;KAC5C;IAED,8BAAO,GAAP;QACE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QACjD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAA;KAC/B;IA4CH,mBAAC;AAAD,CAAC;;AClEM,IAAM,eAAe,GAAG;IAC7B,iBAAiB,EAAE,QAAiC;CACrD,CAAA;AAEM,IAAM,iBAAiB,GAAG;IAC/B,SAAS,EAAE,QAAiD;IAC5D,cAAc,EAAE,QAAsD;IACtE,aAAa,EAAE,QAAqD;IACpE,SAAS,EAAE,QAAiD;IAC5D,gBAAgB,EAAE,QAAwD;IAC1E,eAAe,EAAE,QAAuD;IACxE,WAAW,EAAE,QAAuD;IACpE,IAAI,EAAE,QAA4C;IAClD,YAAY,EAAE,QAAoD;IAClE,UAAU,EAAE,QAAkD;CAC/D;;ACOD;;;;;AAKA;IAOE,iCAAY,QAAyB,EAAE,gBAAoC;QAA3E,iBAQC;QAbD,qBAAgB,GAA2B,IAAI,CAAA;QAC/C,mBAAc,GAAsB,IAAI,CAAA;QACxC,qBAAgB,GAA6B,IAAI,CAAA;QACjD,aAAQ,GAAoB,IAAI,CAAA;QAYhC,oBAAe,GAAG,UAAC,EAAoB;YACrC,KAAI,CAAC,QAAQ,GAAG,KAAI,CAAC,aAAa,CAAC,EAAE,CAAC,SAAwB,CAAC,CAAA;SAChE,CAAA;QAYD,oBAAe,GAAG,UAAC,GAAe,EAAE,OAAgB,EAAE,EAAoB;YAClE,IAAA,QAAQ,GAAK,KAAI,CAAC,WAAW,SAArB,CAAqB;YACnC,IAAI,gBAAgB,GAA2B,IAAI,CAAA;YACnD,IAAI,cAAc,GAAsB,IAAI,CAAA;YAC5C,IAAI,SAAS,GAAG,KAAK,CAAA;YACrB,IAAI,WAAW,GAA0B;gBACvC,cAAc,EAAE,qBAAqB,EAAE;gBACvC,aAAa,EAAE,qBAAqB,EAAE;gBACtC,OAAO,EAAE,KAAI,CAAC,QAAS,CAAC,MAAM;aAC/B,CAAA;YAED,IAAI,GAAG,EAAE;gBACP,gBAAgB,GAAG,GAAG,CAAC,OAAO,CAAA;gBAE9B,IAAI,KAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,SAAwB,EAAE,gBAAgB,CAAC,EAAE;oBAC3E,cAAc,GAAG,uBAAuB,CACtC,GAAG,CAAC,QAAQ,EACZ,KAAI,CAAC,QAAS,EACd,gBAAgB,CACjB,CAAA;oBAED,WAAW,CAAC,aAAa,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAA;oBAC7D,SAAS,GAAG,CAAC,kBAAkB,CAAC,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;oBAE/E,IAAI,SAAS,EAAE;wBACb,WAAW,CAAC,aAAa,GAAG,qBAAqB,EAAE,CAAA;wBACnD,cAAc,GAAG,IAAI,CAAA;qBACtB;iBACF;aACF;YAED,KAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAA;;;YAI/C,QAAQ,CAAC,kBAAkB,CACzB,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAE1E,CAAA;YAED,IAAI,CAAC,SAAS,EAAE;gBACd,YAAY,EAAE,CAAA;aACf;iBAAM;gBACL,aAAa,EAAE,CAAA;aAChB;YAED,IAAI,CAAC,OAAO,EAAE;gBACZ,QAAQ,CAAC,oBAAoB,CAAC,CAAC,cAAc,CAAC,CAAA;gBAE9C,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;gBACxC,KAAI,CAAC,cAAc,GAAG,cAAc,CAAA;aACrC;SACF,CAAA;QAED,kBAAa,GAAG,UAAC,GAAqB;YAChC,IAAA,KAAuC,KAAI,EAAzC,gBAAgB,sBAAA,EAAE,cAAc,oBAAS,CAAA;YAE/C,KAAI,CAAC,SAAS,EAAE,CAAA;YAEhB,IAAI,gBAAgB,IAAI,cAAc,EAAE;gBACtC,IAAI,QAAQ,GAAG,KAAI,CAAC,WAAW,CAAC,QAAS,CAAA;gBACzC,IAAI,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAA;gBACxC,IAAI,QAAQ,GAAG,KAAI,CAAC,QAAS,CAAA;gBAE7B,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,wBAClC,4BAA4B,CAAC,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,CAAC,KACpE,SAAS,EAAE,GAAG,CAAC,SAAwB,EACvC,OAAO,EAAE,GAAG,CAAC,SAAuB,EACpC,IAAI,EAAE,SAAS,IACf,CAAA;gBAEF,IAAI,QAAQ,CAAC,MAAM,EAAE;oBACnB,IAAI,cAAY,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAA;oBAEpD,gBAAgB,CAAC,QAAQ,CAAC;wBACxB,IAAI,EAAE,cAAc;wBACpB,UAAU,EAAE,cAAY;qBACzB,CAAC,CAAA;oBAEF,IAAI,GAAG,CAAC,OAAO,EAAE;wBACf,gBAAgB,CAAC,QAAQ,CAAC;4BACxB,IAAI,EAAE,cAAc;4BACpB,eAAe,EAAE,cAAc,CAAC,QAAQ,CAAC,UAAU;yBACpD,CAAC,CAAA;qBACH;;oBAGD,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE;wBAC/C,KAAK,EAAE,IAAI,QAAQ,CACjB,gBAAgB,EAChB,cAAc,CAAC,GAAG,EAClB,cAAc,CAAC,QAAQ,CACxB;wBACD,aAAa,EAAE,EAAE;wBACjB,MAAM;4BACJ,gBAAgB,CAAC,QAAQ,CAAC;gCACxB,IAAI,EAAE,eAAe;gCACrB,UAAU,EAAE,cAAY;6BACzB,CAAC,CAAA;yBACH;wBACD,SAAS,EAAE,GAAG,CAAC,SAAwB;wBACvC,IAAI,EAAE,SAAS;qBAChB,CAAC,CAAA;iBACH;aACF;YAED,KAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;YAC5B,KAAI,CAAC,cAAc,GAAG,IAAI,CAAA;SAC3B,CAAA;QAnIC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,QAAQ,EAAE,wBAAwB,CAAC,CAAA;QACxF,WAAW,CAAC,cAAc,GAAG,KAAK,CAAA;QAClC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QACzD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;QAErD,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;KACzC;IAMD,+CAAa,GAAb,UAAc,SAAsB;QAClC,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,QAAQ,EAAE;YAC7C,OAAO,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;SAC5C;QACD,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE;YAC/C,OAAO,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAA;SACvD;QACD,OAAO,iBAAiB,CAAC,SAAS,CAAC,CAAA;KACpC;IAgHD,6CAAW,GAAX,UAAY,WAAmC,EAAE,KAA4B;QAC3E,IAAI,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAA;QAEvC,IAAI,WAAW,IAAI,WAAW,KAAK,WAAW,EAAE;YAC9C,WAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;SACnD;QAED,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,OAAA,EAAE,CAAC,CAAA;SACxD;KACF;IAED,2CAAS,GAAT;QACE,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;SAC7D;KACF;IAED,qDAAmB,GAAnB,UAAoB,EAAe,EAAE,gBAAiC;QACpE,IAAI,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAA;QAEpD,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;YACpC,OAAO,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;SACzD;QAED,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,EAAE;YAChD,OAAO,OAAO,CAAC,cAAc,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,CAAA;SAC/C;QAED,OAAO,IAAI,CAAA;KACZ;IACH,8BAAC;AAAD,CAAC,IAAA;AAED;AACA;AAEA,SAAS,uBAAuB,CAAC,QAAkB,EAAE,QAAkB,EAAE,OAAwB;IAC/F,IAAI,QAAQ,gBAAQ,QAAQ,CAAC,aAAa,CAAE,CAAA;IAE5C,KAAsB,UAAyC,EAAzC,KAAA,OAAO,CAAC,WAAW,CAAC,qBAAqB,EAAzC,cAAyC,EAAzC,IAAyC,EAAE;QAA5D,IAAI,SAAS,SAAA;QAChB,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAA;KAClD;IAEG,IAAA,KAAqB,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,EAApD,OAAO,aAAA,EAAE,KAAK,WAAsC,CAAA;IAC1D,IAAI,GAAG,GAAG,aAAa,CACrB,OAAO,EACP,KAAK,EACL,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,MAAM,EACf,OAAO,CAAC,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAChE,OAAO,CACR,CAAA;IAED,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAA;;;IAIhC,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,SAAS,EAAE;QACzC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAA;KACvD;IAED,IAAI,GAAG,GAAG,QAAQ,CAAC,QAAQ;QACzB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;QAC7C,kBAAkB,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IAErD,IAAI,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,OAAA,EAAE,GAAG,KAAA,EAAE,CAAC,CAAA;IAE7D,OAAO,EAAE,GAAG,KAAA,EAAE,QAAQ,UAAA,EAAE,CAAA;AAC1B,CAAC;AAED;AACA;AAEA,SAAS,iBAAiB,CAAC,EAAe;IACxC,IAAI,GAAG,GAAG,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;IACxC,IAAI,GAAG,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACf,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;IAEnB,OAAO,aAAa,CAAC,GAAG,CAAC,CAAA;AAC3B,CAAC;AAED,MAAM,CAAC,cAAc,GAAG,EAAE,CAAA;AAE1B,SAAS,iBAAiB,CAAC,EAAe,EAAE,IAAY;IACtD,IAAI,MAAM,GAAG,MAAM,CAAC,cAAc,CAAA;IAClC,IAAI,YAAY,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,GAAG,GAAG,EAAE,IAAI,IAAI,CAAA;IAEtD,OAAO,EAAE,CAAC,YAAY,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,EAAE,CAAA;AACtD;;AC/PA;;;;;;IASE,2BAAY,EAAe,EAAE,QAAwC;QAArE,iBAkBC;QAlB4B,yBAAA,EAAA,aAAwC;QAoBrE,sBAAiB,GAAG,UAAC,EAAoB;YACjC,IAAA,QAAQ,GAAK,KAAI,SAAT,CAAS;YACnB,IAAA,KAAkC,KAAI,CAAC,QAAQ,EAA7C,WAAW,iBAAA,EAAE,cAAc,oBAAkB,CAAA;YAEnD,QAAQ,CAAC,WAAW;gBAClB,WAAW,IAAI,IAAI;oBACjB,WAAW;qBACV,EAAE,CAAC,OAAO,GAAG,CAAC,GAAG,oBAAoB,CAAC,oBAAoB,CAAC,CAAA;YAEhE,QAAQ,CAAC,KAAK;gBACZ,EAAE,CAAC,OAAO;qBACP,cAAc,IAAI,IAAI,GAAG,cAAc,GAAG,oBAAoB,CAAC,cAAc;oBAC9E,CAAC,CAAA;SACN,CAAA;QAED,oBAAe,GAAG,UAAC,EAAoB;YACrC,IACE,EAAE,CAAC,OAAO;gBACV,KAAI,CAAC,QAAQ,CAAC,KAAK;gBAClB,EAAE,CAAC,SAAyB,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAC5D;gBACA,KAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA;aACtE;SACF,CAAA;QA1CC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAExB,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,yBAAyB,CAAC,EAAE,CAAC,CAAA;QAChE,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAA;QAEnC,IAAI,QAAQ,CAAC,YAAY,IAAI,IAAI,EAAE;YACjC,QAAQ,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAA;SAClD;QAED,IAAI,QAAQ,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC7B,QAAQ,CAAC,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAA;SAC/C;QAED,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAC1D,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,CAAA;QAEtD,IAAI,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAA;KAC1D;IA2BD,mCAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;KACxB;IACH,wBAAC;AAAD,CAAC;;AClED;;;;;;AAMA;IAA6C,2CAAe;IAM1D,iCAAY,WAAwB;QAApC,YACE,kBAAM,WAAW,CAAC,SAMnB;QAXD,sBAAgB,GAAY,KAAK,CAAA;QACjC,oBAAc,GAAW,EAAE,CAAA;QAC3B,qBAAe,GAAuB,IAAI,CAAA;QAe1C,uBAAiB,GAAG,UAAC,EAAoB;YACvC,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;YAEvC,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;;gBAE1B,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;aACtC;SACF,CAAA;QAED,uBAAiB,GAAG,UAAC,EAAoB;YACvC,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;gBAC1B,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;aACrC;SACF,CAAA;QAED,qBAAe,GAAG,UAAC,EAAoB;YACrC,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;YAErC,IAAI,CAAC,KAAI,CAAC,gBAAgB,EAAE;;gBAE1B,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;aACpC;SACF,CAAA;QAhCC,IAAI,OAAO,GAAG,KAAI,CAAC,OAAO,GAAG,IAAI,eAAe,CAAC,WAAW,CAAC,CAAA;QAC7D,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,CAAA;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,KAAI,CAAC,iBAAiB,CAAC,CAAA;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,eAAe,CAAC,CAAA;;KACtD;IAED,yCAAO,GAAP;QACE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;KACvB;IA0BD,+CAAa,GAAb,UAAc,IAAa;QACzB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;KAC7B;IAED,oDAAkB,GAAlB,UAAmB,IAAa;QAC9B,IAAI,IAAI,EAAE;;;YAGR,IAAI,IAAI,CAAC,eAAe,EAAE;gBACxB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAA;gBAC1C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;aAC5B;SACF;aAAM;YACL,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc;;kBAE9B,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAgB;kBAC1D,IAAI,CAAA;YAER,IAAI,QAAQ,EAAE;gBACZ,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAA;gBAC/B,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAA;aACrC;SACF;KACF;IACH,8BAAC;AAAD,CAnEA,CAA6C,eAAe;;ACA5D;;;;;IAOE,6BACE,mBAA+D,EAC/D,QAAsC;QAEtC,IAAI,WAAW,GAAgB,QAAQ,CAAA;QAEvC;;QAEE,mBAAmB,KAAK,QAAQ;YAChC,mBAAmB,YAAY,OAAO,EACtC;YACA,WAAW,GAAG,mBAAkC,CAAA;YAChD,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAA;SAC1B;aAAM;YACL,QAAQ,IAAI,mBAAmB,IAAI,EAAE,CAAgC,CAAA;SACtE;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,uBAAuB,CAAC,WAA0B,CAAC,CAAA;QAEtF,IAAI,OAAO,QAAQ,CAAC,YAAY,KAAK,QAAQ,EAAE;YAC7C,QAAQ,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAA;SAClD;aAAM,IAAI,WAAW,KAAK,QAAQ,EAAE;YACnC,QAAQ,CAAC,OAAO,CAAC,QAAQ,GAAG,cAAc,CAAA;SAC3C;QAED,IAAI,OAAO,QAAQ,CAAC,cAAc,KAAK,QAAQ,EAAE;YAC/C,QAAQ,CAAC,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAA;SAClD;QAED,IAAI,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAA;KAC1D;IAED,qCAAO,GAAP;QACE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;KACxB;IACH,0BAAC;AAAD,CAAC;;ACzCD,WAAe,YAAY,CAAC;IAC1B,qBAAqB,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;IAClF,oBAAoB,EAAE,CAAC,YAAY,CAAC;IACpC,mBAAmB,EAAE,yBAAyB;IAC9C,cAAc,EAAE,eAAe;IAC/B,gBAAgB,EAAE,iBAAiB;CACpC,CAAC;;;;;"}