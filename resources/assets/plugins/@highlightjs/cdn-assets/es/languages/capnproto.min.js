/*! `capnproto` grammar compiled for Highlight.js 11.4.0 */
var hljsGrammar=(()=>{"use strict";return t=>{const n={variants:[{
match:[/(struct|enum|interface)/,/\s+/,t.IDENT_RE]},{
match:[/extends/,/\s*\(/,t.IDENT_RE,/\s*\)/]}],scope:{1:"keyword",
3:"title.class"}};return{name:"Cap\u2019n Proto",aliases:["capnp"],keywords:{
keyword:["struct","enum","interface","union","group","import","using","const","annotation","extends","in","of","on","as","with","from","fixed"],
type:["Void","Bool","Int8","Int16","Int32","Int64","UInt8","UInt16","UInt32","UInt64","Float32","Float64","Text","Data","AnyPointer","AnyStruct","Capability","List"],
literal:["true","false"]},
contains:[t.QUOTE_STRING_MODE,t.NUMBER_MODE,t.HASH_COMMENT_MODE,{
className:"meta",begin:/@0x[\w\d]{16};/,illegal:/\n/},{className:"symbol",
begin:/@\d+\b/},n]}}})();export default hljsGrammar;