/*! `twig` grammar compiled for Highlight.js 11.4.0 */
(()=>{var e=(()=>{"use strict";return e=>{
const a=e.regex,n=["attribute","block","constant","country_timezones","cycle","date","dump","html_classes","include","max","min","parent","random","range","source","template_from_string"]
;let t=["apply","autoescape","block","cache","deprecated","do","embed","extends","filter","flush","for","from","if","import","include","macro","sandbox","set","use","verbatim","with"]
;t=t.concat(t.map((e=>"end"+e)));const r={scope:"string",variants:[{begin:/'/,
end:/'/},{begin:/"/,end:/"/}]},c={scope:"number",match:/\d+/},o={begin:/\(/,
end:/\)/,excludeBegin:!0,excludeEnd:!0,contains:[r,c]},s={
beginKeywords:n.join(" "),keywords:{name:n},relevance:0,contains:[o]},i={
match:/\|(?=[A-Za-z_]+:?)/,beginScope:"punctuation",relevance:0,contains:[{
match:/[A-Za-z_]+:?/,
keywords:["abs","batch","capitalize","column","convert_encoding","country_name","currency_name","currency_symbol","data_uri","date","date_modify","default","escape","filter","first","format","format_currency","format_date","format_datetime","format_number","format_time","html_to_markdown","inky_to_html","inline_css","join","json_encode","keys","language_name","last","length","locale_name","lower","map","markdown","markdown_to_html","merge","nl2br","number_format","raw","reduce","replace","reverse","round","slice","slug","sort","spaceless","split","striptags","timezone_name","title","trim","u|0","upper","url_encode"]
}]},m=(e,{relevance:n})=>({beginScope:{1:"template-tag",3:"name"},
relevance:n||2,endScope:"template-tag",begin:[/\{%/,/\s*/,a.either(...e)],
end:/%\}/,keywords:"in",contains:[i,s,r,c]}),l=m(t,{relevance:2
}),d=m([/[a-z_]+/],{relevance:1});return{name:"Twig",aliases:["craftcms"],
case_insensitive:!0,subLanguage:"xml",contains:[e.COMMENT(/\{#/,/#\}/),l,d,{
className:"template-variable",begin:/\{\{/,end:/\}\}/,contains:["self",i,s,r,c]
}]}}})();hljs.registerLanguage("twig",e)})();