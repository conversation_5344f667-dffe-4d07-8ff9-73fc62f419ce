
// Show add tournament modal
function showAddTournamentModal() {
    $('#addTournamentModal').modal('show');
    // Auto-focus the native select when the modal is shown
    $('#addTournamentModal').on('shown.bs.modal', function () {
        $('#id_pais_modal').trigger('focus');
    });
}

// Add selected tournament from modal
function addSelectedTournamentFromModal() {
    const selectedPaisId = document.getElementById('id_pais_modal').value;
    if (selectedPaisId) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'epartido_probabilidades';
        
        // Add hidden fields
        const idPartidoField = document.createElement('input');
        idPartidoField.type = 'hidden';
        idPartidoField.name = 'id_partido';
        idPartidoField.value = document.getElementById('id_partido').value;
        form.appendChild(idPartidoField);
        
        const tabSelectedField = document.createElement('input');
        tabSelectedField.type = 'hidden';
        tabSelectedField.name = 'tabselected';
        tabSelectedField.value = document.getElementById('tabselected').value;
        form.appendChild(tabSelectedField);
        
        const tabSelectedCornersField = document.createElement('input');
        tabSelectedCornersField.type = 'hidden';
        tabSelectedCornersField.name = 'tabselected_corners';
        tabSelectedCornersField.value = document.getElementById('tabselected_corners').value;
        form.appendChild(tabSelectedCornersField);
        
        const idPaisField = document.createElement('input');
        idPaisField.type = 'hidden';
        idPaisField.name = 'id_pais';
        idPaisField.value = selectedPaisId;
        form.appendChild(idPaisField);
        
        document.body.appendChild(form);
        form.submit();
    } else {
        alert('Please select a tournament to add.');
    }
}

// Show create tournament modal
function showCreateTournamentModal() {
    $('#createTournamentModal').modal('show');
    // Auto-focus the tournament name field when modal is shown
    $('#createTournamentModal').on('shown.bs.modal', function () {
        $('#tournament_name_create').focus();
    });
}

// Show edit tournament modal
function openEditTournamentModal(tournamentId, tournamentName) {
    document.getElementById('tournament_id_edit').value = tournamentId;
    document.getElementById('tournament_name_edit').value = tournamentName;
    $('#editTournamentModal').modal('show');
    // Auto-focus the tournament name field when modal is shown
    $('#editTournamentModal').on('shown.bs.modal', function () {
        $('#tournament_name_edit').focus().select();
    });
}

// Handle create tournament form submission
document.getElementById('createTournamentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('sub_create_tournament', '1');
    formData.append('tournament_name', document.getElementById('tournament_name_create').value);
    formData.append('id_partido', document.getElementById('id_partido').value);
    formData.append('tabselected', document.getElementById('tabselected').value);
    
    fetch('epartido_probabilidades', {
        method: 'POST',
        body: formData
    })
        .then(response => response.text())
        .then(data => {
            // Close the modal and reload the page
            $('#createTournamentModal').modal('hide');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error creating tournament. Please try again.');
        });
});

// Handle edit tournament form submission
document.getElementById('editTournamentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('sub_edit_tournament', '1');
    formData.append('tournament_id', document.getElementById('tournament_id_edit').value);
    formData.append('tournament_name', document.getElementById('tournament_name_edit').value);
    formData.append('id_partido', document.getElementById('id_partido').value);
    formData.append('tabselected', document.getElementById('tabselected').value);
    
    fetch('epartido_probabilidades', {
        method: 'POST',
        body: formData
    })
        .then(response => response.text())
        .then(data => {
            // Close the modal and reload the page
            $('#editTournamentModal').modal('hide');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating tournament. Please try again.');
        });
});

$(document).ready(function() {
    // Store original data and current partido info
    const originalRows = [];
    const partidoHome = '<?php echo addslashes($cur_partido->home); ?>';
    const partidoAway = '<?php echo addslashes($cur_partido->away); ?>';
    
    // Store original table rows
    $('#historico-table tbody tr').each(function(index) {
        const row = $(this);
        const partidoInfoHome = row.find('td:nth-child(4)').text().trim(); // Home is 4th column
        const partidoInfoAway = row.find('td:nth-child(8)').text().trim(); // Away is 8th column (after Total corners)
        
        originalRows.push({
            element: row.clone(),
            home: partidoInfoHome,
            away: partidoInfoAway,
            rowNumber: index + 1,
            separatorClass: row.hasClass('historical-separator-5') ? 'historical-separator-5' :
                (row.hasClass('historical-separator-10') ? 'historical-separator-10' : '')
        });
    });
    
    // Debug: Log the team names for comparison
    console.log('Current partido home:', partidoHome);
    console.log('Current partido away:', partidoAway);
    console.log('Sample row data:', originalRows.length > 0 ? originalRows[0] : 'No rows found');
    
    // Initialize with 'todos' filter to show all records
    filterHistorico('todos');
    
    // Filter function
    function filterHistorico(filterType) {
        let filteredRows = [];
        console.log('Filtering with type:', filterType);
        
        originalRows.forEach(function(rowData) {
            let showRow = false;
            
            // Normalize strings for comparison (trim and handle potential encoding issues)
            const normalizedRowHome = rowData.home.trim();
            const normalizedRowAway = rowData.away.trim();
            const normalizedPartidoHome = partidoHome.trim();
            const normalizedPartidoAway = partidoAway.trim();
            
            switch(filterType) {
                case 'todos':
                    showRow = true;
                    break;
                case 'home':
                    // Home: Show ALL matches played by the Home team (both when playing at home AND when playing away)
                    showRow = (normalizedRowHome === normalizedPartidoHome || normalizedRowAway === normalizedPartidoHome);
                    break;
                case 'home_h':
                    // Home @H: Show records where the Home team played at home (PartidoInfo.home = Partido.home)
                    showRow = (normalizedRowHome === normalizedPartidoHome);
                    break;
                case 'home_a':
                    // Home @A: Show records where the Home team played away (PartidoInfo.away = Partido.home)
                    showRow = (normalizedRowAway === normalizedPartidoHome);
                    break;
                case 'away':
                    // Away: Show ALL matches played by the Away team (both when playing at home AND when playing away)
                    showRow = (normalizedRowHome === normalizedPartidoAway || normalizedRowAway === normalizedPartidoAway);
                    break;
                case 'away_a':
                    // Away @A: Show records where the Away team played away (PartidoInfo.away = Partido.away)
                    showRow = (normalizedRowAway === normalizedPartidoAway);
                    break;
                case 'away_h':
                    // Away @H: Show records where the Away team played at home (PartidoInfo.home = Partido.away)
                    showRow = (normalizedRowHome === normalizedPartidoAway);
                    break;
            }
            
            if (showRow) {
                filteredRows.push(rowData);
            }
        });
        
        // Debug: Log filtering results
        console.log('Filtered rows count:', filteredRows.length);
        console.log('Total original rows:', originalRows.length);
        
        // Clear current table
        $('#historico-table tbody').empty();
        
        // Add filtered rows with correct numbering and separators
        filteredRows.forEach(function(rowData, index) {
            const newRow = rowData.element.clone();
            
            // Update row number
            newRow.find('td:first-child').text(index + 1);
            
            // Remove old separator classes
            newRow.removeClass('historical-separator-5 historical-separator-10');
            
            // Add separator classes at positions 5 and 10
            if (index + 1 === 5) {
                newRow.addClass('historical-separator-5');
            } else if (index + 1 === 10) {
                newRow.addClass('historical-separator-10');
            }
            
            $('#historico-table tbody').append(newRow);
        });
        
        // Update counter
        $('#historico-counter').text(filteredRows.length);
    }
    
    // Handle filter button clicks
    $('#historico-filters label').on('click', function() {
        const filterType = $(this).data('filter');
        
        // Update active state
        $('#historico-filters label').removeClass('active');
        $(this).addClass('active');
        
        // Apply filter
        filterHistorico(filterType);
    });
    
    // Excel export functionality
    $('#export-excel-btn').on('click', function() {
        exportToExcel();
    });
    
    function exportToExcel() {
        // Get current filter (fallback to 'todos' if none active)
        let activeFilter = $('#historico-filters label.active').data('filter');
        if (!activeFilter) { activeFilter = 'todos'; }
        
        // Show loading state
        $('#export-excel-btn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Exporting...');
        
        // Create form data for POST request
        const formData = new FormData();
        formData.append('id_partido', '<?php echo $cur_partido->id; ?>');
        formData.append('filter_type', activeFilter);
        formData.append('partido_home', partidoHome);
        formData.append('partido_away', partidoAway);
        
        // Create XMLHttpRequest for file download
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '<?php echo RUTA; ?>src/export_historico_excel.php', true);
        xhr.responseType = 'blob';
        // Optional timeout to avoid indefinite hanging
        xhr.timeout = 120000; // 120s
        
        xhr.onload = function() {
            try {
                if (xhr.status === 200) {
                    // Create download link with correct XLSX MIME and sanitized filename
                    const blob = new Blob([xhr.response], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    const unsafeName = 'historico_' + partidoHome + '_vs_' + partidoAway + '_' + activeFilter + '.xlsx';
                    const safeName = unsafeName.replace(/[\\/:*?"<>|]/g, '_');
                    link.download = safeName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    
                    // Show success message
                    toastr.success('Excel file downloaded successfully');
                } else {
                    // Attempt to read error message if provided
                    toastr.error('Error generating Excel file');
                    console.error('Export error:', xhr.status, xhr.statusText);
                }
            } catch (e) {
                console.error('Export exception:', e);
                toastr.error('Unexpected error during export');
            } finally {
                // Reset button state
                $('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
            }
        };
        
        xhr.onerror = function() {
            toastr.error('Network error occurred while exporting');
            $('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
        };
        
        xhr.onabort = function() {
            toastr.warning('Export aborted');
            $('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
        };
        
        xhr.ontimeout = function() {
            toastr.error('Export timed out');
            $('#export-excel-btn').prop('disabled', false).html('<i class="fa fa-file-excel"></i> Export Excel');
        };
        
        xhr.send(formData);
    }
});
